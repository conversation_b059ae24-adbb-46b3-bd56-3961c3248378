BEGIN;


CREATE TABLE IF NOT EXISTS "ams_HAUL_DISPATCH_LOCATION_TYPES" (
    code VARCHAR(40) PRIMARY KEY,
    label VARCHAR(40) NOT NULL,
    description VARCHAR(50) NOT NULL
);

INSERT INTO "ams_HAUL_DISPATCH_LOCATION_TYPES" (code, "label", description)
VALUES
    ('SHIFT', 'Shift', 'Shift'),
    ('TRIP', 'Trip', 'Trip')
ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS "ams_haul_dispatch_locations" (
    asset_id VARCHAR(40) NOT NULL PRIMARY KEY,
    load_location_id VARCHAR(40) REFERENCES "ams_locations" (id),
    dump_location_id VARCHAR(40) REFERENCES "ams_locations" (id),
    dispatch_location_type_code VARCHAR(40) REFERENCES "ams_HAUL_DISPATCH_LOCATION_TYPES" (code),
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);

CREATE TABLE IF NOT EXISTS "ams_haul_load_statuses" (
    asset_id VARCHAR(40) NOT NULL PRIMARY KEY,
    is_load BOOLEAN NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    updated_by VARCHAR(40) NOT NULL
);

CREATE OR REPLACE FUNCTION upsert_haul_load_statuses()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.haul_status_code = 'READY' THEN
        INSERT INTO ams_haul_load_statuses 
        (asset_id, is_load, client_id, updated_at, updated_by)
        VALUES (NEW.asset_id, (
            SELECT is_load
            FROM "ams_HAUL_ACTIVITIES"
            WHERE code = NEW.haul_activity_code
        ), NEW.client_id, NEW.created_at, NEW.created_by)
        ON CONFLICT (asset_id) DO UPDATE
        SET is_load = EXCLUDED.is_load, updated_at = EXCLUDED.updated_at, updated_by = EXCLUDED.updated_by;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS upsert_haul_load_statuses ON ams_haulings;

CREATE TRIGGER upsert_haul_load_statuses
AFTER INSERT OR UPDATE ON ams_haulings
FOR EACH ROW
EXECUTE FUNCTION upsert_haul_load_statuses();

ALTER TABLE "ams_driver_login_sessions"
ADD COLUMN IF NOT EXISTS "start_vehicle_hm" NUMERIC(20,2),
ADD COLUMN IF NOT EXISTS "end_vehicle_hm" NUMERIC(20,2),
ADD COLUMN IF NOT EXISTS "start_vehicle_km" NUMERIC(20,0),
ADD COLUMN IF NOT EXISTS "end_vehicle_km" NUMERIC(20,0),
ADD COLUMN IF NOT EXISTS "start_fuel_consumed" NUMERIC(20,2),
ADD COLUMN IF NOT EXISTS "end_fuel_consumed" NUMERIC(20,2),
ADD COLUMN IF NOT EXISTS "vehicle_hm" NUMERIC(20,2),
ADD COLUMN IF NOT EXISTS "vehicle_km" NUMERIC(20,0),
ADD COLUMN IF NOT EXISTS "fuel_consumed" NUMERIC(20,2),
ADD COLUMN IF NOT EXISTS "vehicle_km_per_hm" NUMERIC(20,2) GENERATED ALWAYS AS (CASE WHEN (vehicle_hm = 0 OR vehicle_hm IS NULL) THEN NULL ELSE vehicle_km / vehicle_hm END) STORED,
ADD COLUMN IF NOT EXISTS "fuel_consumed_per_km" NUMERIC(20,2) GENERATED ALWAYS AS (CASE WHEN (vehicle_km = 0 OR vehicle_km IS NULL) THEN NULL ELSE fuel_consumed / vehicle_km END) STORED,
ADD COLUMN IF NOT EXISTS "total_net_weight" DECIMAL(12,2),
ADD COLUMN IF NOT EXISTS "total_trip" SMALLINT;

CREATE TABLE IF NOT EXISTS "ams_HAUL_PERFORMANCE_INDICATOR_TYPES" (
    code VARCHAR(50) PRIMARY KEY,
    label VARCHAR(100) NOT NULL,
    description VARCHAR(255) NOT NULL,
    unit VARCHAR(20) NOT NULL
);

INSERT INTO "ams_HAUL_PERFORMANCE_INDICATOR_TYPES" (code, label, description, unit)
VALUES
    ('PRODUCTIVITY_KM_PER_HM', 'Productivity (km/hm)', 'Vehicle productivity measured in km/hm', 'km/hm'),
    ('FUEL_RATIO', 'Fuel Ratio', 'Fuel consumption ratio measured in liter/km', 'liter/km')
ON CONFLICT (code) DO NOTHING;


CREATE TABLE IF NOT EXISTS ams_haul_performace_indicators (
    code VARCHAR(100) PRIMARY KEY,
    label VARCHAR(100) NOT NULL,
    performance_indicator_type_code VARCHAR(50) REFERENCES "ams_HAUL_PERFORMANCE_INDICATOR_TYPES" (code),
    lower_limit NUMERIC(20,2),
    upper_limit NUMERIC(20,2)
);

-- Productivity (km/hm) indicators
INSERT INTO ams_haul_performace_indicators (code, label, performance_indicator_type_code, lower_limit, upper_limit)
VALUES
    ('KM_PER_HM_EXCEPTIONAL', 'Exceptional', 'PRODUCTIVITY_KM_PER_HM', 25.20, NULL),
    ('KM_PER_HM_EXCELLENT', 'Excellent', 'PRODUCTIVITY_KM_PER_HM', 22.70, 25.19),
    ('KM_PER_HM_GOOD', 'Good', 'PRODUCTIVITY_KM_PER_HM', 20.20, 22.69),
    ('KM_PER_HM_AVERAGE', 'Average', 'PRODUCTIVITY_KM_PER_HM', 17.70, 20.19),
    ('KM_PER_HM_FAIR', 'Fair', 'PRODUCTIVITY_KM_PER_HM', 15.10, 17.69),
    ('KM_PER_HM_POOR', 'Poor', 'PRODUCTIVITY_KM_PER_HM', NULL, 15.09);

-- Fuel Ratio (liter/km) indicators  
INSERT INTO ams_haul_performace_indicators (code, label, performance_indicator_type_code, lower_limit, upper_limit)
VALUES
    ('FUEL_RATIO_EXCELLENT', 'Excellent', 'FUEL_RATIO', NULL, 1.54),
    ('FUEL_RATIO_GOOD', 'Good', 'FUEL_RATIO', 1.55, 1.58),
    ('FUEL_RATIO_AVERAGE', 'Average', 'FUEL_RATIO', 1.59, 1.62),
    ('FUEL_RATIO_FAIR', 'Fair', 'FUEL_RATIO', 1.63, 1.66),
    ('FUEL_RATIO_POOR', 'Poor', 'FUEL_RATIO', 1.67, NULL);

INSERT INTO "uis_DEVICE_TYPES" VALUES
    ('DRIVER_APP_MOBILE', 'Driver App Mobile', 'Driver App Mobile') ON CONFLICT (code) DO NOTHING;
    
COMMIT;