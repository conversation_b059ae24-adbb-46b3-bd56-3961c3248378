package dtos

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	trackingModel "assetfindr/internal/app/geo/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"math"
	"time"

	"gopkg.in/guregu/null.v4"
)

type DriverCheckIn struct {
	UserID string `json:"user_id"`
	PIN    string `json:"pin" binding:"required,min=6"`
}

type DriverCheckOut struct {
	FirebaseDeviceToken string          `json:"firebase_device_token"`
	DeviceTypeRef       string          `json:"device_type_ref"`
	ReportDiffKMHM      *ReportDiffKMHM `json:"report_diff_km_hm"`
}

type HaulActivityListReq struct {
	HaulStatusCode    string `form:"haul_status_code"`
	IsWithSubActivity bool   `form:"is_with_sub_activity"`
}

type HaulSubActivityListReq struct {
	MainActivityCode string `form:"main_activity_code"`
}

type DriverLoginSessionResp struct {
	AssetID       string    `json:"asset_id"`
	UserID        string    `json:"user_id"`
	UserFullName  string    `json:"user_full_name"`
	LoggedInTime  time.Time `json:"logged_in_time"`
	LoggedOutTime null.Time `json:"logged_out_time"`

	VehicleKM         null.Float `json:"vehicle_km"`
	VehicleHM         null.Float `json:"vehicle_hm"`
	FuelConsumed      null.Float `json:"fuel_consumed"`
	VehicleKMPerHm    null.Float `json:"vehicle_km_per_hm"`
	FuelConsumedPerKm null.Float `json:"fuel_consumed_per_km"`
	TotalNetWeight    null.Float `json:"total_net_weight"`
	TotalTrip         null.Int   `json:"total_trip"`

	Asset *HaulingAssetIdent `json:"asset"`
}

type VerifyDriverCheckInResp struct {
	CurrentLoginSession *DriverLoginSessionResp `json:"current_login_session"`
	CurrentHauling      *HaulingResp            `json:"current_hauling"`
}

// ExampleVerifyDriverCheckInResp returns an example response for VerifyDriverCheckInResp
func ExampleVerifyDriverCheckInResp() VerifyDriverCheckInResp {
	return VerifyDriverCheckInResp{
		CurrentLoginSession: &DriverLoginSessionResp{
			AssetID:           "asset-123",
			UserID:            "user-456",
			UserFullName:      "John Doe",
			LoggedInTime:      time.Date(2024, 1, 15, 8, 30, 0, 0, time.UTC),
			LoggedOutTime:     null.Time{},
			VehicleKM:         null.FloatFrom(15420.5),
			VehicleHM:         null.FloatFrom(2850.2),
			FuelConsumed:      null.FloatFrom(125.8),
			VehicleKMPerHm:    null.FloatFrom(5.41),
			FuelConsumedPerKm: null.FloatFrom(0.0082),
			TotalNetWeight:    null.FloatFrom(45.5),
			TotalTrip:         null.IntFrom(12),
			Asset: &HaulingAssetIdent{
				AssetID:         "asset-123",
				Name:            "Dump Truck DT-001",
				SerialNumber:    "DT001SN789",
				ReferenceNumber: "REF-DT-001",
			},
		},
		CurrentHauling: &HaulingResp{
			AssetID:             "asset-123",
			OperatorUserID:      "user-456",
			HaulStatusCode:      "LOADING",
			HaulActivityCode:    "LOAD_MATERIAL",
			HaulSubActivityCode: null.StringFrom("LOAD_COAL"),
			StartTime:           time.Date(2024, 1, 15, 9, 0, 0, 0, time.UTC),
			EndTime:             null.Time{},
			HaulStatus: HaulStatusResp{
				Code:        "LOADING",
				Label:       "Loading",
				Description: "Vehicle is currently loading material",
				Rank:        2,
			},
			HaulActivity: HaulActivityResp{
				Code:           "LOAD_MATERIAL",
				Label:          "Load Material",
				Description:    "Loading material into the vehicle",
				HaulStatusCode: "LOADING",
				Rank:           1,
				IsLoad:         null.BoolFrom(true),
				HaulSubActivities: []HaulSubActivityResp{
					{
						Code:             "LOAD_COAL",
						Label:            "Load Coal",
						Description:      "Loading coal material",
						MainActivityCode: "LOAD_MATERIAL",
						Rank:             null.IntFrom(1),
					},
				},
			},
			HaulSubActivity: &HaulSubActivityResp{
				Code:             "LOAD_COAL",
				Label:            "Load Coal",
				Description:      "Loading coal material",
				MainActivityCode: "LOAD_MATERIAL",
				Rank:             null.IntFrom(1),
			},
			NextHaulActivity: &HaulActivityResp{
				Code:              "TRANSPORT",
				Label:             "Transport",
				Description:       "Transporting material to destination",
				HaulStatusCode:    "HAULING",
				Rank:              2,
				IsLoad:            null.BoolFrom(false),
				HaulSubActivities: []HaulSubActivityResp{},
			},
			PrevHaulActivity: &HaulActivityResp{
				Code:              "QUEUE_LOAD",
				Label:             "Queue for Loading",
				Description:       "Waiting in queue for loading",
				HaulStatusCode:    "QUEUING",
				Rank:              0,
				IsLoad:            null.BoolFrom(false),
				HaulSubActivities: []HaulSubActivityResp{},
			},
		},
	}
}

type CheckOutDriverLoginSessionResp struct {
	AssetID       string    `json:"asset_id"`
	UserID        string    `json:"user_id"`
	UserFullName  string    `json:"user_full_name"`
	LoggedInTime  time.Time `json:"logged_in_time"`
	LoggedOutTime null.Time `json:"logged_out_time"`

	VehicleKM         null.Float `json:"vehicle_km"`
	VehicleHM         null.Float `json:"vehicle_hm"`
	FuelConsumed      null.Float `json:"fuel_consumed"`
	VehicleKMPerHm    null.Float `json:"vehicle_km_per_hm"`
	FuelConsumedPerKm null.Float `json:"fuel_consumed_per_km"`
	TotalNetWeight    null.Float `json:"total_net_weight"`
	TotalTrip         null.Int   `json:"total_trip"`
}

func BuildDriverLoginSessionResp(loginSession *models.DriverLoginSession) DriverLoginSessionResp {
	if loginSession == nil {
		return DriverLoginSessionResp{}
	}

	asset := BuildHaulingAssetIdent(loginSession.Asset)

	resp := DriverLoginSessionResp{
		AssetID:       loginSession.AssetID,
		UserID:        loginSession.UserID,
		UserFullName:  loginSession.UserFullName,
		LoggedInTime:  loginSession.LoggedInTime,
		LoggedOutTime: loginSession.LoggedOutTime,
		Asset:         &asset,
	}

	return resp
}

func BuildCheckOutDriverLoginSessionResp(loginSession *models.DriverLoginSession) CheckOutDriverLoginSessionResp {
	if loginSession == nil {
		return CheckOutDriverLoginSessionResp{}
	}

	resp := CheckOutDriverLoginSessionResp{
		AssetID:           loginSession.AssetID,
		UserID:            loginSession.UserID,
		UserFullName:      loginSession.UserFullName,
		LoggedInTime:      loginSession.LoggedInTime,
		LoggedOutTime:     loginSession.LoggedOutTime,
		VehicleKM:         loginSession.VehicleKm,
		VehicleHM:         loginSession.VehicleHm,
		FuelConsumed:      loginSession.FuelConsumed,
		VehicleKMPerHm:    loginSession.VehicleKmPerHm,
		FuelConsumedPerKm: loginSession.FuelConsumedPerKm,
		TotalNetWeight:    loginSession.TotalNetWeight,
		TotalTrip:         loginSession.TotalTrip,
	}

	return resp
}

type HaulStatusResp struct {
	Code        string `json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
	Rank        int    `json:"rank"`
}

type HaulActivityResp struct {
	Code           string    `json:"code"`
	Label          string    `json:"label"`
	Description    string    `json:"description"`
	HaulStatusCode string    `json:"haul_status_code"`
	Rank           int       `json:"rank"`
	IsLoad         null.Bool `json:"is_load"`

	HaulSubActivities []HaulSubActivityResp `json:"haul_sub_activities"`
}

type HaulSubActivityResp struct {
	Code             string   `json:"code"`
	Label            string   `json:"label"`
	Description      string   `json:"description"`
	MainActivityCode string   `json:"main_activity_code"`
	Rank             null.Int `json:"rank"`
}

func BuildHaulStatusResp(haulStatus models.HaulStatus) HaulStatusResp {
	return HaulStatusResp{
		Code:        haulStatus.Code,
		Label:       haulStatus.Label,
		Description: haulStatus.Description,
		Rank:        haulStatus.Rank,
	}
}

func BuildHaulActivityResp(haulActivity models.HaulActivity) HaulActivityResp {
	haulSubActivities := make([]HaulSubActivityResp, 0, len(haulActivity.HaulSubActivities))
	for _, haulSubActivity := range haulActivity.HaulSubActivities {
		haulSubActivities = append(haulSubActivities, BuildHaulSubActivityResp(haulSubActivity))
	}
	return HaulActivityResp{
		Code:              haulActivity.Code,
		Label:             haulActivity.Label,
		Description:       haulActivity.Description,
		HaulStatusCode:    haulActivity.HaulStatusCode,
		Rank:              haulActivity.Rank,
		IsLoad:            haulActivity.IsLoad,
		HaulSubActivities: haulSubActivities,
	}
}

func BuildHaulSubActivityResp(haulSubActivity models.HaulSubActivity) HaulSubActivityResp {
	return HaulSubActivityResp{
		Code:             haulSubActivity.Code,
		Label:            haulSubActivity.Label,
		Description:      haulSubActivity.Description,
		MainActivityCode: haulSubActivity.MainActivityCode,
		Rank:             haulSubActivity.Rank,
	}
}

type GetCurrentHaulingResp struct {
	Current HaulingResp  `json:"current"`
	Ready   *HaulingResp `json:"ready"`
}

type HaulingResp struct {
	AssetID             string               `json:"asset_id"`
	OperatorUserID      string               `json:"operator_user_id"`
	HaulStatusCode      string               `json:"haul_status_code"`
	HaulActivityCode    string               `json:"haul_activity_code"`
	HaulSubActivityCode null.String          `json:"haul_sub_activity_code"`
	StartTime           time.Time            `json:"start_time"`
	EndTime             null.Time            `json:"end_time"`
	HaulStatus          HaulStatusResp       `json:"haul_status"`
	HaulActivity        HaulActivityResp     `json:"haul_activity"`
	HaulSubActivity     *HaulSubActivityResp `json:"haul_sub_activity"`
	NextHaulActivity    *HaulActivityResp    `json:"next_haul_activity"`
	PrevHaulActivity    *HaulActivityResp    `json:"prev_haul_activity"`
}

func BuildHaulingResp(hauling *models.Hauling) HaulingResp {
	resp := HaulingResp{
		AssetID:             hauling.AssetID,
		OperatorUserID:      hauling.OperatorUserID,
		HaulStatusCode:      hauling.HaulStatusCode,
		HaulActivityCode:    hauling.HaulActivityCode,
		HaulSubActivityCode: hauling.HaulSubActivityCode,
		StartTime:           hauling.StartTime,
		EndTime:             hauling.EndTime,
		HaulStatus:          BuildHaulStatusResp(hauling.HaulStatus),
		HaulActivity:        BuildHaulActivityResp(hauling.HaulActivity),
		HaulSubActivity:     nil,
	}

	if hauling.HaulSubActivity != nil {
		haulSubActivity := BuildHaulSubActivityResp(*hauling.HaulSubActivity)
		resp.HaulSubActivity = &haulSubActivity
	}

	if hauling.HaulActivity.NextActivityCycle != nil {
		nextHaulActivity := BuildHaulActivityResp(hauling.HaulActivity.NextActivityCycle.NextActivity)
		resp.NextHaulActivity = &nextHaulActivity
	}

	if hauling.HaulActivity.PrevActivityCycle != nil {
		prevHaulActivity := BuildHaulActivityResp(hauling.HaulActivity.PrevActivityCycle.PrevActivity)
		resp.PrevHaulActivity = &prevHaulActivity
	}

	return resp
}

type HaulingUpdateStatusReq struct {
	Action                    string      `json:"action"`
	CustomHaulStatusCode      string      `json:"custom_haul_status_code"`
	CustomHaulActivityCode    string      `json:"custom_haul_activity_code"`
	CustomHaulSubActivityCode null.String `json:"custom_haul_sub_activity_code"`
}

func (r *HaulingUpdateStatusReq) Validate() error {
	switch r.Action {
	case constants.HAUL_STATUS_CHANGE_ACTION_NEXT,
		constants.HAUL_STATUS_CHANGE_ACTION_PREV,
		constants.HAUL_STATUS_CHANGE_ACTION_CONTINUE_READY:
		return nil
	case constants.HAUL_STATUS_CHANGE_ACTION_CUSTOM:
		for index, code := range constants.ValidCustomHaulStatusCodes {
			if r.CustomHaulStatusCode == code {
				break
			}
			if index == len(constants.ValidCustomHaulStatusCodes)-1 {
				return errorhandler.ErrBadRequest("invalid custom_haul_status_code")
			}
		}
		if r.CustomHaulActivityCode == "" {
			return errorhandler.ErrBadRequest("custom_haul_activity_code is required")
		}
		return nil
	case "":
		return errorhandler.ErrBadRequest("action is required")
	default:
		return errorhandler.ErrBadRequest("invalid action")
	}
}

type GetPublicCurrentHaulingResp struct {
	ClientID          string            `json:"client_id"`
	Current           *HaulingResp      `json:"current"`
	Asset             HaulingAssetIdent `json:"asset"`
	LatestKMHMIoTData LatestKMHMIoTData `json:"latest_iot_data"`
}

type LatestKMHMIoTData struct {
	VehicleKM  null.Float `json:"vehicle_km"`
	VehicleHM  null.Float `json:"vehicle_hm"`
	LatestTime null.Time  `json:"latest_time"`
}

type HaulingAssetIdent struct {
	AssetID         string `json:"asset_id"`
	Name            string `json:"name"`
	SerialNumber    string `json:"serial_number"`
	ReferenceNumber string `json:"reference_number"`
}

func BuildHaulingAssetIdent(asset models.Asset) HaulingAssetIdent {
	return HaulingAssetIdent{
		AssetID:         asset.ID,
		Name:            asset.Name,
		SerialNumber:    asset.SerialNumber,
		ReferenceNumber: asset.ReferenceNumber,
	}
}

type GetHaulingStatusBoardReq struct {
	SearchKeyword         string    `form:"search_keyword"`
	HaulStatusCodes       []string  `form:"haul_status_codes"`
	HaulActivityCodes     []string  `form:"haul_activity_codes"`
	HaulSubActivityCodes  []string  `form:"haul_sub_activity_codes"`
	AssetIDs              []string  `form:"asset_ids"`
	IsLoad                null.Bool `form:"is_load"`
	IsOver12HLoginSession bool      `form:"is_over_12h_login_session"`
	IsLoadingPointEmpty   bool      `form:"is_loading_point_empty"`
	IsDumpingPointEmpty   bool      `form:"is_dumping_point_empty"`
}

type HaulingStatusBoardItem struct {
	AssetID                 string               `json:"asset_id"`
	OperatorUserID          string               `json:"operator_user_id"`
	HaulStatusCode          string               `json:"haul_status_code"`
	HaulActivityCode        string               `json:"haul_activity_code"`
	HaulSubActivityCode     null.String          `json:"haul_sub_activity_code"`
	StartTime               time.Time            `json:"start_time"`
	EndTime                 null.Time            `json:"end_time"`
	IsHasEmptyDispatchPoint bool                 `json:"is_has_empty_dispatch_point"`
	HaulSubActivity         *HaulSubActivityResp `json:"haul_sub_activity"`
	IsLoad                  bool                 `json:"is_load"`

	Asset                     HaulingAssetIdent       `json:"asset"`
	CurrentDriverLoginSession *DriverLoginSessionResp `json:"current_driver_login_session"`
}

func BuildHaulingStatusBoardItem(hauling models.Hauling) HaulingStatusBoardItem {
	resp := HaulingStatusBoardItem{
		AssetID:             hauling.AssetID,
		OperatorUserID:      hauling.OperatorUserID,
		HaulStatusCode:      hauling.HaulStatusCode,
		HaulActivityCode:    hauling.HaulActivityCode,
		HaulSubActivityCode: hauling.HaulSubActivityCode,
		StartTime:           hauling.StartTime,
		EndTime:             hauling.EndTime,
		IsLoad:              hauling.HaulActivity.IsLoad.Bool,
		HaulSubActivity:     nil,
		Asset: HaulingAssetIdent{
			AssetID: hauling.AssetID,
		},
		CurrentDriverLoginSession: nil,
	}

	if hauling.LoadStatus != nil {
		resp.IsLoad = hauling.LoadStatus.IsLoad
	}

	if hauling.DispacthLocation != nil {
		resp.IsHasEmptyDispatchPoint = hauling.DispacthLocation.LoadLocationID.String == "" ||
			hauling.DispacthLocation.DumpLocationID.String == ""
	}

	if hauling.Asset != nil {
		resp.Asset.Name = hauling.Asset.Name
		resp.Asset.SerialNumber = hauling.Asset.SerialNumber
		resp.Asset.ReferenceNumber = hauling.Asset.ReferenceNumber
	}

	if hauling.HaulSubActivity != nil {
		haulSubActivity := BuildHaulSubActivityResp(*hauling.HaulSubActivity)
		resp.HaulSubActivity = &haulSubActivity
	}

	if hauling.CurrentDriverLoginSession != nil {
		currentDriverLoginSession := BuildDriverLoginSessionResp(hauling.CurrentDriverLoginSession)
		resp.CurrentDriverLoginSession = &currentDriverLoginSession
	}

	return resp
}

type HaulingStatusBoardResp struct {
	Items []HaulingBoardStatus `json:"items"`
}

type HaulingBoardStatus struct {
	HaulStatusCode         string                 `json:"haul_status_code"`
	Label                  string                 `json:"label"`
	Rank                   int                    `json:"rank"`
	Count                  int                    `json:"count"`
	HaulingBoardActivities []HaulingBoardActivity `json:"haul_activities"`
}

type HaulingBoardActivity struct {
	HaulActivityCode string                   `json:"haul_activity_code"`
	Label            string                   `json:"label"`
	Rank             int                      `json:"rank"`
	IsLoad           null.Bool                `json:"is_load"`
	Count            int                      `json:"count"`
	Items            []HaulingStatusBoardItem `json:"items"`
}

func BuildHaulingStatusBoardResp(haulings []models.Hauling) HaulingStatusBoardResp {
	haulingsGroupedByStatus := make(map[int][]models.Hauling)
	for _, hauling := range haulings {
		haulingsGroupedByStatus[hauling.HaulStatus.Rank] = append(haulingsGroupedByStatus[hauling.HaulStatus.Rank], hauling)
	}

	haulingsBoardStatus := make([]HaulingBoardStatus, 0, len(haulingsGroupedByStatus))
	for _, hauling := range haulingsGroupedByStatus {
		haulingsBoardStatus = append(haulingsBoardStatus, BuildHaulingBoardStatus(hauling))
	}

	return HaulingStatusBoardResp{
		Items: haulingsBoardStatus,
	}
}

func BuildHaulingBoardStatus(haulings []models.Hauling) HaulingBoardStatus {
	if len(haulings) == 0 {
		return HaulingBoardStatus{}
	}

	haulingsGroupedByActivity := make(map[int][]models.Hauling)
	for _, hauling := range haulings {
		haulingsGroupedByActivity[hauling.HaulActivity.Rank] = append(haulingsGroupedByActivity[hauling.HaulActivity.Rank], hauling)
	}

	haulingsBoardActivities := make([]HaulingBoardActivity, 0, len(haulingsGroupedByActivity))
	for _, hauling := range haulingsGroupedByActivity {
		haulingsBoardActivities = append(haulingsBoardActivities, BuildHaulingBoardActivity(hauling))
	}

	return HaulingBoardStatus{
		HaulStatusCode:         haulings[0].HaulStatusCode,
		Label:                  haulings[0].HaulStatus.Label,
		Rank:                   haulings[0].HaulStatus.Rank,
		Count:                  len(haulings),
		HaulingBoardActivities: haulingsBoardActivities,
	}
}

func BuildHaulingBoardActivity(haulings []models.Hauling) HaulingBoardActivity {
	if len(haulings) == 0 {
		return HaulingBoardActivity{}
	}

	items := make([]HaulingStatusBoardItem, 0, len(haulings))
	for _, hauling := range haulings {
		items = append(items, BuildHaulingStatusBoardItem(hauling))
	}

	return HaulingBoardActivity{
		HaulActivityCode: haulings[0].HaulActivityCode,
		Label:            haulings[0].HaulActivity.Label,
		Rank:             haulings[0].HaulActivity.Rank,
		IsLoad:           haulings[0].HaulActivity.IsLoad,
		Count:            len(haulings),
		Items:            items,
	}
}

type CurrentAssetHaulingResp struct {
	AssetID             string                     `json:"asset_id"`
	OperatorUserID      string                     `json:"operator_user_id"`
	HaulStatusCode      string                     `json:"haul_status_code"`
	HaulActivityCode    string                     `json:"haul_activity_code"`
	HaulSubActivityCode null.String                `json:"haul_sub_activity_code"`
	StartTime           time.Time                  `json:"start_time"`
	EndTime             null.Time                  `json:"end_time"`
	HaulStatus          HaulStatusResp             `json:"haul_status"`
	HaulActivity        HaulActivityResp           `json:"haul_activity"`
	DriverLoginSession  *CurrentDriverLoginSession `json:"driver_login_session"`
	Asset               HaulingAssetIdent          `json:"asset"`
}

func BuildCurrentAssetHaulingResp(
	hauling *models.Hauling,
) CurrentAssetHaulingResp {
	resp := CurrentAssetHaulingResp{
		AssetID:             hauling.AssetID,
		OperatorUserID:      hauling.OperatorUserID,
		HaulStatusCode:      hauling.HaulStatusCode,
		HaulActivityCode:    hauling.HaulActivityCode,
		HaulSubActivityCode: hauling.HaulSubActivityCode,
		StartTime:           hauling.StartTime,
		EndTime:             hauling.EndTime,
		HaulStatus:          BuildHaulStatusResp(hauling.HaulStatus),
		HaulActivity:        BuildHaulActivityResp(hauling.HaulActivity),
		DriverLoginSession:  nil,
	}

	if hauling.Asset != nil {
		resp.Asset = BuildHaulingAssetIdent(*hauling.Asset)
	}

	return resp
}

type CurrentDriverLoginSession struct {
	AssetID        string     `json:"asset_id"`
	UserID         string     `json:"user_id"`
	UserFullName   string     `json:"user_full_name"`
	LoggedInTime   time.Time  `json:"logged_in_time"`
	LoggedOutTime  null.Time  `json:"logged_out_time"`
	TotalNetWeight null.Float `json:"total_net_weight"`
	TotalTrip      null.Int   `json:"total_trip"`

	VehicleHm         null.Float `json:"vehicle_hm"`
	VehicleKm         null.Float `json:"vehicle_km"`
	FuelConsumed      null.Float `json:"fuel_consumed"`
	VehicleKMPerHm    null.Float `json:"vehicle_km_per_hm"`
	FuelConsumedPerKm null.Float `json:"fuel_consumed_per_km"`
}

func BuildDriverLoginSession(dls *models.DriverLoginSession) CurrentDriverLoginSession {
	return CurrentDriverLoginSession{
		AssetID:           dls.AssetID,
		UserID:            dls.UserID,
		UserFullName:      dls.UserFullName,
		LoggedInTime:      dls.LoggedInTime,
		LoggedOutTime:     dls.LoggedOutTime,
		VehicleHm:         dls.VehicleHm,
		VehicleKm:         dls.VehicleKm,
		FuelConsumed:      dls.FuelConsumed,
		VehicleKMPerHm:    dls.VehicleKmPerHm,
		FuelConsumedPerKm: dls.FuelConsumedPerKm,
	}
}

func BuildCurrentDriverLoginSession(dls *models.DriverLoginSession,
	latestCanBusData *trackingModel.LatestCanBusSensorForHauling,
	totalNetWeightAndTrip *models.WeightBridgeTicketTotalNetWeightAndTrip,
) CurrentDriverLoginSession {
	resp := CurrentDriverLoginSession{
		AssetID:       dls.AssetID,
		UserID:        dls.UserID,
		UserFullName:  dls.UserFullName,
		LoggedInTime:  dls.LoggedInTime,
		LoggedOutTime: dls.LoggedOutTime,
	}

	if latestCanBusData != nil {
		if latestCanBusData.CanEngineMotorhours.Valid && dls.StartVehicleHm.Valid {
			resp.VehicleHm = null.FloatFrom(latestCanBusData.CanEngineMotorhours.Float64 - dls.StartVehicleHm.Float64)
		}

		if latestCanBusData.CanVehicleMileage.Valid && dls.StartVehicleKm.Valid {
			resp.VehicleKm = null.FloatFrom(latestCanBusData.CanVehicleMileage.Float64 - dls.StartVehicleKm.Float64)
			if resp.VehicleHm.Valid && resp.VehicleHm.Float64 != 0 {
				resp.VehicleKMPerHm = null.FloatFrom(resp.VehicleKm.Float64 / resp.VehicleHm.Float64)
			}
		}

		if latestCanBusData.CanFuelConsumed.Valid && dls.StartFuelConsumed.Valid {
			resp.FuelConsumed = null.FloatFrom(latestCanBusData.CanFuelConsumed.Float64 - dls.StartFuelConsumed.Float64)
			if resp.VehicleKm.Valid && resp.VehicleKm.Float64 != 0 {
				resp.FuelConsumedPerKm = null.FloatFrom(resp.FuelConsumed.Float64 / resp.VehicleKm.Float64)
			}
		}

		resp.VehicleHm.Float64 = calculationhelpers.RoundToTwoDecimals(resp.VehicleHm.Float64)
		resp.VehicleKm.Float64 = math.Round(resp.VehicleKm.Float64)
		resp.FuelConsumed.Float64 = calculationhelpers.RoundToTwoDecimals(resp.FuelConsumed.Float64)
		resp.VehicleKMPerHm.Float64 = calculationhelpers.RoundToTwoDecimals(resp.VehicleKMPerHm.Float64)
		resp.FuelConsumedPerKm.Float64 = calculationhelpers.RoundToTwoDecimals(resp.FuelConsumedPerKm.Float64)
	}

	if totalNetWeightAndTrip != nil {
		resp.TotalNetWeight = null.FloatFrom(totalNetWeightAndTrip.TotalNetWeight)
		resp.TotalTrip = null.IntFrom(totalNetWeightAndTrip.TotalTrip)
	}

	return resp
}

func (r *CurrentAssetHaulingResp) BuildCurrentDriverLoginSession(
	dls *models.DriverLoginSession,
	latestCanBusData *trackingModel.LatestCanBusSensorForHauling,
	totalNetWeightAndTrip *models.WeightBridgeTicketTotalNetWeightAndTrip,
) {
	driverLoginSession := BuildCurrentDriverLoginSession(dls, latestCanBusData, totalNetWeightAndTrip)
	r.DriverLoginSession = &driverLoginSession
}

type AssetHaulingUpdateStatusReq struct {
	HaulStatusCode      string      `json:"haul_status_code"`
	HaulActivityCode    string      `json:"haul_activity_code"`
	HaulSubActivityCode null.String `json:"haul_sub_activity_code"`
	Comment             string      `json:"comment"`
}

type ReportDiffKMHM struct {
	Photos []commonmodel.PhotoReq `json:"photos"`
}
