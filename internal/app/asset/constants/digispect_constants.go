package constants

const (
	DIGISPECT_STATUS_ACTIVE   = "ACTIVE"
	DIGISPECT_STATUS_INACTIVE = "INACTIVE"

	DIGISPECT_STATUS_ACTIVE_LABEL   = "Active"
	DIGISPECT_STATUS_INACTIVE_LABEL = "Inactive"
)

func MapDigispectStatus() map[string]string {
	return map[string]string{
		DIGISPECT_STATUS_ACTIVE:   DIGISPECT_STATUS_ACTIVE_LABEL,
		DIGISPECT_STATUS_INACTIVE: DIGISPECT_STATUS_INACTIVE_LABEL,
	}
}

const (
	DIGISPECT_TYPE_VEHICLE = "VEHICLE"
	DIGISPECT_TYPE_TYRE    = "TYRE"

	DIGISPECT_TYPE_VEHICLE_LABEL = "Vehicle"
	DIGISPECT_TYPE_TYRE_LABEL    = "Tyre"
)

func MapDigispectType() map[string]string {
	return map[string]string{
		DIGISPECT_TYPE_VEHICLE: DIGISPECT_TYPE_VEHICLE_LABEL,
		DIGISPECT_TYPE_TYRE:    DIGISPECT_TYPE_TYRE_LABEL,
	}
}
