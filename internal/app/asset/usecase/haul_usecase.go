package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	geoRepo "assetfindr/internal/app/geo/repository"
	integrationConstant "assetfindr/internal/app/integration/constants"
	integrationModels "assetfindr/internal/app/integration/models"
	integrationRepo "assetfindr/internal/app/integration/repository"
	taskConstants "assetfindr/internal/app/task/constants"
	taskModels "assetfindr/internal/app/task/models"
	taskRepo "assetfindr/internal/app/task/repository"
	userConstants "assetfindr/internal/app/user-identity/constants"
	userDtos "assetfindr/internal/app/user-identity/dtos"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userModels "assetfindr/internal/app/user-identity/models"
	userIdentityRepo "assetfindr/internal/app/user-identity/repository"
	userIdentityUsecase "assetfindr/internal/app/user-identity/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/firebaseApp"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"context"
	"math"
	"time"

	"firebase.google.com/go/v4/messaging"
	"github.com/jackc/pgtype"

	"gopkg.in/guregu/null.v4"
)

type HaulUseCase struct {
	DB                     database.DBUsecase
	DBTimeScale            database.DBUsecase
	haulRepo               repository.HaulRepository
	integrationRepo        integrationRepo.IntegrationRepository
	userRepo               userIdentityRepo.UserRepository
	userUsecase            *userIdentityUsecase.UserUseCase
	assetRepo              repository.AssetRepository
	trackingRepo           geoRepo.TrackingRepository
	weightBridgeTicketRepo repository.WeightBridgeRepository
	locationRepo           repository.LocationRepository
	ticketRepo             taskRepo.TicketRepository
}

func NewHaulUseCase(
	DB database.DBUsecase,
	DBTimeScale database.DBUsecase,
	haulRepo repository.HaulRepository,
	integrationRepo integrationRepo.IntegrationRepository,
	userRepo userIdentityRepo.UserRepository,
	userUsecase *userIdentityUsecase.UserUseCase,
	assetRepo repository.AssetRepository,
	trackingRepo geoRepo.TrackingRepository,
	weightBridgeTicketRepo repository.WeightBridgeRepository,
	locationRepo repository.LocationRepository,
	ticketRepo taskRepo.TicketRepository,
) *HaulUseCase {
	return &HaulUseCase{
		DB:                     DB,
		DBTimeScale:            DBTimeScale,
		haulRepo:               haulRepo,
		integrationRepo:        integrationRepo,
		userRepo:               userRepo,
		userUsecase:            userUsecase,
		assetRepo:              assetRepo,
		trackingRepo:           trackingRepo,
		weightBridgeTicketRepo: weightBridgeTicketRepo,
		locationRepo:           locationRepo,
		ticketRepo:             ticketRepo,
	}
}

func (uc *HaulUseCase) VerifyDriverCheckIn(ctx context.Context, imei string, req dtos.DriverCheckIn) (*commonmodel.DetailResponse, error) {
	userCLient, err := uc.userRepo.GetUserClient(ctx, uc.DB.DB(), userModels.UserClientCondition{
		Where: userModels.UserClientWhere{
			UnencryptedPIN: req.PIN,
			UserID:         req.UserID,
		},
		Preload: userModels.UserClientPreload{
			User: true,
		},
	})
	if err != nil {
		if errorhandler.IsErrNotFound(err) {
			return nil, errorhandler.ErrBadRequest("Invalid PIN")
		}

		return nil, err
	}

	if userCLient.User == nil {
		return nil, errorhandler.ErrBadRequest("User not found")
	}

	if !userCLient.User.IsActive() {
		return nil, errorhandler.ErrNotAllowed("trying to login in inactive user, please contact your administrator")
	}

	integrationDevice, err := uc.integrationRepo.GetIntegrationDevice(ctx, uc.DB.DB(), integrationModels.IntegrationDeviceCondition{
		Where: integrationModels.IntegrationDeviceWhere{
			ReferenceCode:       imei,
			IntegrationTypeCode: integrationConstant.INTEGRATION_TYPE_CODE_DEVICE_TABLET,
			ClientID:            userCLient.ClientID,
		},
		Preload: integrationModels.IntegrationDevicePreload{
			ActiveIntegration: true,
		},
	})
	if err != nil {
		return nil, err
	}

	if integrationDevice.Integration.ID == "" {
		return nil, errorhandler.ErrBadRequest("device not found")
	}

	currDriverLoginSession, err := uc.haulRepo.GetDriverLoginSession(ctx, uc.DB.DB(), models.DriverLoginSessionCondition{
		Where: models.DriverLoginSessionWhere{
			UserID:     req.UserID,
			NotAssetID: integrationDevice.Integration.InternalReferenceID,
			ClientID:   userCLient.ClientID,
			IsActive:   null.BoolFrom(true),
		},
		Preload: models.DriverLoginSessionPreload{
			Asset: true,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	currHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
		Where: models.HaulingWhere{
			AssetID:  integrationDevice.Integration.InternalReferenceID,
			IsActive: null.BoolFrom(true),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	resp := dtos.VerifyDriverCheckInResp{
		CurrentLoginSession: nil,
		CurrentHauling:      nil,
	}

	if currDriverLoginSession != nil {
		temp := dtos.BuildDriverLoginSessionResp(currDriverLoginSession)
		resp.CurrentLoginSession = &temp
	}

	if currHauling != nil {
		temp := dtos.BuildHaulingResp(currHauling)
		resp.CurrentHauling = &temp
	}

	return &commonmodel.DetailResponse{
		Success: true,
		Message: "Success",
		Data:    resp,
	}, nil
}

func (uc *HaulUseCase) DriverCheckIn(ctx context.Context, imei string, req dtos.DriverCheckIn) (*userDtos.UserAuthResponse, error) {
	userCLient, err := uc.userRepo.GetUserClient(ctx, uc.DB.DB(), userModels.UserClientCondition{
		Where: userModels.UserClientWhere{
			UnencryptedPIN: req.PIN,
			UserID:         req.UserID,
		},
		Preload: userModels.UserClientPreload{
			User: true,
		},
	})
	if err != nil {
		if errorhandler.IsErrNotFound(err) {
			return nil, errorhandler.ErrBadRequest("Invalid PIN")
		}

		return nil, err
	}

	if userCLient.User == nil {
		return nil, errorhandler.ErrBadRequest("User not found")
	}

	if !userCLient.User.IsActive() {
		return nil, errorhandler.ErrNotAllowed("trying to login in inactive user, please contact your administrator")
	}

	clientID := userCLient.ClientID

	integrationDevice, err := uc.integrationRepo.GetIntegrationDevice(ctx, uc.DB.DB(), integrationModels.IntegrationDeviceCondition{
		Where: integrationModels.IntegrationDeviceWhere{
			ReferenceCode:       imei,
			IntegrationTypeCode: integrationConstant.INTEGRATION_TYPE_CODE_DEVICE_TABLET,
			ClientID:            clientID,
		},
		Preload: integrationModels.IntegrationDevicePreload{
			ActiveIntegration: true,
		},
	})
	if err != nil {
		return nil, err
	}

	if integrationDevice.Integration.ID == "" {
		return nil, errorhandler.ErrBadRequest("device not found")
	}

	err = uc.DriverCheckOutFromOtherVehicles(ctx, userCLient.UserID, integrationDevice.Integration.InternalReferenceID)
	if err != nil {
		return nil, err
	}

	currDriverLoginSession, err := uc.haulRepo.GetDriverLoginSession(ctx, uc.DB.DB(), models.DriverLoginSessionCondition{
		Where: models.DriverLoginSessionWhere{
			AssetID:  integrationDevice.Integration.InternalReferenceID,
			IsActive: null.BoolFrom(true),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	if currDriverLoginSession != nil {
		if currDriverLoginSession.UserID != userCLient.UserID {
			return nil, errorhandler.ErrBadRequest("Asset is already logged in by another user")
		}
	} else {
		latestCanBusData, err := uc.trackingRepo.LatestCanBusSensorForHauling(ctx, uc.DBTimeScale.DB(),
			integrationDevice.Integration.InternalReferenceID, clientID)
		if err != nil {
			return nil, err
		}

		err = uc.haulRepo.CreateDriverLoginSession(ctx, tx.DB(), &models.DriverLoginSession{
			ModelV2: commonmodel.ModelV2{
				UpdatedBy: userCLient.UserID,
				CreatedBy: userCLient.UserID,
				ClientID:  clientID,
			},
			AssetID:           integrationDevice.Integration.InternalReferenceID,
			UserID:            userCLient.UserID,
			UserFullName:      userCLient.User.GetName(),
			LoggedInTime:      time.Now(),
			StartVehicleHm:    latestCanBusData.CanEngineMotorhours,
			StartVehicleKm:    latestCanBusData.CanVehicleMileage,
			StartFuelConsumed: latestCanBusData.CanFuelConsumed,
		})
		if err != nil {
			return nil, err
		}
	}

	activeHauling, err := uc.haulRepo.GetHauling(ctx, tx.DB(), models.HaulingCondition{
		Where: models.HaulingWhere{
			AssetID:  integrationDevice.Integration.InternalReferenceID,
			IsActive: null.BoolFrom(true),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	isNeedCreateHauling := activeHauling == nil

	if activeHauling != nil && !activeHauling.IsInChangeShiftActivity() {
		err := uc.haulRepo.UpdateHauling(ctx, tx.DB(), activeHauling.ID, &models.Hauling{
			EndTime: null.TimeFrom(time.Now()),
		})
		if err != nil {
			return nil, err
		}

		isNeedCreateHauling = true
	}

	if isNeedCreateHauling {
		err = uc.haulRepo.CreateHauling(ctx, tx.DB(), &models.Hauling{
			ModelV2: commonmodel.ModelV2{
				UpdatedBy: userCLient.UserID,
				CreatedBy: userCLient.UserID,
				ClientID:  clientID,
			},
			AssetID:             integrationDevice.Integration.InternalReferenceID,
			OperatorUserID:      userCLient.UserID,
			HaulStatusCode:      constants.HAUL_STATUS_CODE_DELAY,
			HaulActivityCode:    constants.HAUL_ACTIVITY_CODE_CHANGE_SHIFT,
			HaulSubActivityCode: null.String{},
			StartTime:           time.Now(),
		})
		if err != nil {
			return nil, err
		}

		err = uc.haulRepo.InitHaulLoadStatus(ctx, tx.DB(), &models.HaulLoadStatus{
			AssetID: integrationDevice.Integration.InternalReferenceID,
		})
		if err != nil {
			return nil, err
		}
	}

	resp, err := uc.userUsecase.UserLoginByUserClient(ctx, tx, userCLient)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (uc *HaulUseCase) DriverCheckOutFromOtherVehicles(ctx context.Context, userID, currAssetID string) error {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	defer tx.Rollback()

	otherDriverLoginSessions, err := uc.haulRepo.GetDriverLoginSessions(ctx, tx.DB(), models.DriverLoginSessionCondition{
		Where: models.DriverLoginSessionWhere{
			UserID:     userID,
			NotAssetID: currAssetID,
			IsActive:   null.BoolFrom(true),
		},
	})
	if err != nil {
		return err
	}

	for _, driverLoginSession := range otherDriverLoginSessions {
		updateDriverSession, err := uc.GetUpdateCheckOutDriverLoginSession(ctx, tx, &driverLoginSession)
		if err != nil {
			return err
		}

		err = uc.haulRepo.UpdateDriverLoginSession(ctx, tx.DB(), driverLoginSession.ID, updateDriverSession)
		if err != nil {
			return err
		}

		err = uc.userRepo.InActiveUserDeviceDriverApp(ctx, tx.DB(), userID, driverLoginSession.ClientID)
		if err != nil {
			return err
		}
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	return nil
}

func (uc *HaulUseCase) DriverCheckOut(ctx context.Context, imei string, req dtos.DriverCheckOut) (*commonmodel.UpdateResponse, error) {
	currDriverLoginSession, err := uc.ValidateCurrentLogin(ctx, imei)
	if err != nil {
		return nil, err
	}

	activeHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
		Where: models.HaulingWhere{
			AssetID:  currDriverLoginSession.AssetID,
			IsActive: null.BoolFrom(true),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	if activeHauling != nil {
		err := uc.haulRepo.UpdateHauling(ctx, tx.DB(), activeHauling.ID, &models.Hauling{
			EndTime: null.TimeFrom(time.Now()),
		})
		if err != nil {
			return nil, err
		}
	}

	err = uc.haulRepo.CreateHauling(ctx, tx.DB(), &models.Hauling{
		AssetID:             currDriverLoginSession.AssetID,
		OperatorUserID:      currDriverLoginSession.UserID,
		HaulStatusCode:      constants.HAUL_STATUS_CODE_DELAY,
		HaulActivityCode:    constants.HAUL_ACTIVITY_CODE_CHANGE_SHIFT,
		HaulSubActivityCode: null.String{},
		StartTime:           time.Now(),
	})
	if err != nil {
		return nil, err
	}

	updateDriverSession, err := uc.GetUpdateCheckOutDriverLoginSession(ctx, tx, currDriverLoginSession)
	if err != nil {
		return nil, err
	}

	err = uc.haulRepo.UpdateDriverLoginSession(ctx, tx.DB(), currDriverLoginSession.ID, updateDriverSession)
	if err != nil {
		return nil, err
	}

	err = uc.userUsecase.LogoutFromCheckOut(ctx, tx, userDtos.LogoutReq{
		FirebaseDeviceToken: req.FirebaseDeviceToken,
		DeviceTypeRef:       req.DeviceTypeRef,
	})
	if err != nil {
		return nil, err
	}

	if req.ReportDiffKMHM != nil {
		err = uc.ReportDiffKMHMWrapped(ctx, tx, currDriverLoginSession, *req.ReportDiffKMHM)
		if err != nil {
			return nil, err
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        dtos.BuildCheckOutDriverLoginSessionResp(updateDriverSession),
	}, nil
}

func (uc *HaulUseCase) GetUpdateCheckOutDriverLoginSession(ctx context.Context, tx database.DBUsecase, driverLoginSession *models.DriverLoginSession) (*models.DriverLoginSession, error) {
	latestCanBusData, err := uc.trackingRepo.LatestCanBusSensorForHauling(ctx, uc.DBTimeScale.DB(),
		driverLoginSession.AssetID, driverLoginSession.ClientID)
	if err != nil {
		return nil, err
	}

	latestCanBusData.CanEngineMotorhours.Float64 = calculationhelpers.
		RoundToTwoDecimals(latestCanBusData.CanEngineMotorhours.Float64)

	latestCanBusData.CanVehicleMileage.Float64 = math.
		Round(latestCanBusData.CanVehicleMileage.Float64)

	latestCanBusData.CanFuelConsumed.Float64 = calculationhelpers.
		RoundToTwoDecimals(latestCanBusData.CanFuelConsumed.Float64)

	now := time.Now()
	totalNetWeightAndTrip, err := uc.weightBridgeTicketRepo.GetTotalNetWeightAndTrip(ctx, tx.DB(),
		driverLoginSession.AssetID, driverLoginSession.ClientID,
		driverLoginSession.LoggedInTime, now)
	if err != nil {
		return nil, err
	}

	vehicleHm := null.Float{}
	if latestCanBusData.CanEngineMotorhours.Valid && driverLoginSession.StartVehicleHm.Valid {
		vehicleHm = null.FloatFrom(latestCanBusData.CanEngineMotorhours.Float64 - driverLoginSession.StartVehicleHm.Float64)
	}

	vehicleKm := null.Float{}
	if latestCanBusData.CanVehicleMileage.Valid && driverLoginSession.StartVehicleKm.Valid {
		vehicleKm = null.FloatFrom(latestCanBusData.CanVehicleMileage.Float64 - driverLoginSession.StartVehicleKm.Float64)
	}

	fuelConsumed := null.Float{}
	if latestCanBusData.CanFuelConsumed.Valid && driverLoginSession.StartFuelConsumed.Valid {
		fuelConsumed = null.FloatFrom(latestCanBusData.CanFuelConsumed.Float64 - driverLoginSession.StartFuelConsumed.Float64)
	}

	return &models.DriverLoginSession{
		LoggedOutTime:   null.TimeFrom(now),
		EndVehicleHm:    latestCanBusData.CanEngineMotorhours,
		EndVehicleKm:    latestCanBusData.CanVehicleMileage,
		EndFuelConsumed: latestCanBusData.CanFuelConsumed,
		VehicleHm:       vehicleHm,
		VehicleKm:       vehicleKm,
		FuelConsumed:    fuelConsumed,
		TotalNetWeight:  null.FloatFrom(totalNetWeightAndTrip.TotalNetWeight),
		TotalTrip:       null.IntFrom(totalNetWeightAndTrip.TotalTrip),
	}, nil
}

func (uc *HaulUseCase) GetDriverLoginSessionList(ctx context.Context, req dtos.GetDriverLoginSessionListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, driverLoginSessions, err := uc.haulRepo.GetDriverLoginSessionList(ctx, uc.DB.DB(), models.GetDriverLoginSessionListParam{
		ListRequest: req.ListRequest,
		Cond: models.DriverLoginSessionCondition{
			Where: models.DriverLoginSessionWhere{
				ClientID: claim.GetLoggedInClientID(),
				IsActive: req.IsActive,
			},
			Preload: models.DriverLoginSessionPreload{
				Asset: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         dtos.BuildDriverLoginSessionListResp(driverLoginSessions),
	}

	return resp, nil
}

func (uc *HaulUseCase) GetCurrentDriverLoginSession(ctx context.Context, imei string) (*commonmodel.DetailResponse, error) {
	currentLoginSession, err := uc.ValidateCurrentLogin(ctx, imei)
	if err != nil {
		return nil, err
	}

	latestCanBusData, err := uc.trackingRepo.LatestCanBusSensorForHauling(ctx, uc.DBTimeScale.DB(),
		currentLoginSession.AssetID, currentLoginSession.ClientID)
	if err != nil {
		return nil, err
	}

	totalNetWeightAndTrip, err := uc.weightBridgeTicketRepo.GetTotalNetWeightAndTrip(ctx, uc.DB.DB(),
		currentLoginSession.AssetID,
		currentLoginSession.ClientID,
		currentLoginSession.LoggedInTime, time.Now())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data: dtos.BuildCurrentDriverLoginSession(
			currentLoginSession,
			latestCanBusData,
			totalNetWeightAndTrip,
		),
	}, nil
}

func (uc *HaulUseCase) ValidateCurrentLogin(ctx context.Context, imei string) (*models.DriverLoginSession, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationDevice, err := uc.integrationRepo.GetIntegrationDevice(ctx, uc.DB.DB(), integrationModels.IntegrationDeviceCondition{
		Where: integrationModels.IntegrationDeviceWhere{
			ReferenceCode:       imei,
			IntegrationTypeCode: integrationConstant.INTEGRATION_TYPE_CODE_DEVICE_TABLET,
		},
		Preload: integrationModels.IntegrationDevicePreload{
			ActiveIntegration: true,
		},
	})
	if err != nil {
		return nil, err
	}

	if integrationDevice.Integration.ID == "" {
		return nil, errorhandler.ErrBadRequest("device not found")
	}

	currDriverLoginSession, err := uc.haulRepo.GetDriverLoginSession(ctx, uc.DB.DB(), models.DriverLoginSessionCondition{
		Where: models.DriverLoginSessionWhere{
			AssetID:  integrationDevice.Integration.InternalReferenceID,
			IsActive: null.BoolFrom(true),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if currDriverLoginSession == nil {
		return nil, errorhandler.ErrBadRequest("Asset is not logged in")
	}

	if currDriverLoginSession.UserID != claim.UserID {
		return nil, errorhandler.ErrBadRequest("Asset is logged in by another user")
	}

	return currDriverLoginSession, nil
}

func (uc *HaulUseCase) GetHaulStatuses(ctx context.Context) (*commonmodel.ListResponse, error) {
	haulStatuses, err := uc.haulRepo.GetHaulStatuses(ctx, uc.DB.DB(), models.HaulStatusCondition{})
	if err != nil {
		return nil, err
	}

	haulStatusesResp := []dtos.HaulStatusResp{}
	for _, haulStatus := range haulStatuses {
		haulStatusesResp = append(haulStatusesResp, dtos.BuildHaulStatusResp(haulStatus))
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(haulStatuses),
		PageSize:     len(haulStatuses),
		PageNo:       1,
		Data:         haulStatusesResp,
	}, nil
}

func (uc *HaulUseCase) GetHaulActivities(ctx context.Context, req dtos.HaulActivityListReq) (*commonmodel.ListResponse, error) {
	haulActivities, err := uc.haulRepo.GetHaulActivities(ctx, uc.DB.DB(), models.HaulActivityCondition{
		Where: models.HaulActivityWhere{
			HaulStatusCode:     req.HaulStatusCode,
			RankLargerThanZero: true,
		},
		Preload: models.HaulActivityPreload{
			HaulStatus:        true,
			HaulSubActivities: req.IsWithSubActivity,
		},
	})
	if err != nil {
		return nil, err
	}

	haulActivitiesResp := []dtos.HaulActivityResp{}
	for _, haulActivity := range haulActivities {
		haulActivitiesResp = append(haulActivitiesResp, dtos.BuildHaulActivityResp(haulActivity))
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(haulActivities),
		PageSize:     len(haulActivities),
		PageNo:       1,
		Data:         haulActivitiesResp,
	}, nil
}

func (uc *HaulUseCase) GetHaulSubActivities(ctx context.Context, req dtos.HaulSubActivityListReq) (*commonmodel.ListResponse, error) {
	haulSubActivities, err := uc.haulRepo.GetHaulSubActivities(ctx, uc.DB.DB(), models.HaulSubActivityCondition{
		Where: models.HaulSubActivityWhere{
			MainActivityCode: req.MainActivityCode,
		},
		Preload: models.HaulSubActivityPreload{
			MainActivity: true,
		},
	})
	if err != nil {
		return nil, err
	}

	haulSubActivitiesResp := []dtos.HaulSubActivityResp{}
	for _, haulSubActivity := range haulSubActivities {
		haulSubActivitiesResp = append(haulSubActivitiesResp, dtos.BuildHaulSubActivityResp(haulSubActivity))
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(haulSubActivities),
		PageSize:     len(haulSubActivities),
		PageNo:       1,
		Data:         haulSubActivitiesResp,
	}, nil
}

func (uc *HaulUseCase) GetPublicCurrentHauling(ctx context.Context, imei string) (*commonmodel.DetailResponse, error) {
	integrationDevice, err := uc.integrationRepo.GetIntegrationDevice(ctx, uc.DB.DB(), integrationModels.IntegrationDeviceCondition{
		Where: integrationModels.IntegrationDeviceWhere{
			ReferenceCode:       imei,
			IntegrationTypeCode: integrationConstant.INTEGRATION_TYPE_CODE_DEVICE_TABLET,
		},
		Preload: integrationModels.IntegrationDevicePreload{
			ActiveIntegration: true,
		},
	})
	if err != nil {
		return nil, err
	}

	if integrationDevice.Integration.ID == "" {
		return nil, errorhandler.ErrBadRequest("device not found")
	}

	currentHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
		Where: models.HaulingWhere{
			AssetID:  integrationDevice.Integration.InternalReferenceID,
			IsActive: null.BoolFrom(true),
			ClientID: integrationDevice.ClientID,
		},
		Preload: models.HaulingPreload{
			HaulStatus:           true,
			HaulActivityWithNext: true,
			HaulSubActivity:      true,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       integrationDevice.Integration.InternalReferenceID,
			ClientID: integrationDevice.ClientID,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := dtos.GetPublicCurrentHaulingResp{
		ClientID: asset.ClientID,
		Asset: dtos.HaulingAssetIdent{
			AssetID:         asset.ID,
			Name:            asset.Name,
			SerialNumber:    asset.SerialNumber,
			ReferenceNumber: asset.ReferenceNumber,
		},
	}

	latestCanBusData, err := uc.trackingRepo.LatestCanBusSensorForHauling(ctx, uc.DBTimeScale.DB(),
		integrationDevice.Integration.InternalReferenceID, integrationDevice.ClientID)
	if err != nil {
		return nil, err
	}

	if currentHauling != nil {
		currentHauling := dtos.BuildHaulingResp(currentHauling)
		resp.Current = &currentHauling
	}

	resp.LatestKMHMIoTData = dtos.LatestKMHMIoTData{
		VehicleKM:  latestCanBusData.CanVehicleMileage,
		VehicleHM:  latestCanBusData.CanEngineMotorhours,
		LatestTime: latestCanBusData.LatestTime,
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}

func (uc *HaulUseCase) GetCurrentHauling(ctx context.Context, imei string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	currDriverLoginSession, err := uc.ValidateCurrentLogin(ctx, imei)
	if err != nil {
		return nil, err
	}

	currentHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
		Where: models.HaulingWhere{
			AssetID:  currDriverLoginSession.AssetID,
			IsActive: null.BoolFrom(true),
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.HaulingPreload{
			HaulStatus:           true,
			HaulActivityWithNext: true,
			HaulActivityWithPrev: true,
			HaulSubActivity:      true,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := dtos.GetCurrentHaulingResp{
		Current: dtos.BuildHaulingResp(currentHauling),
	}

	if !currentHauling.IsInReadyStatus() {
		latestReadyHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
			Where: models.HaulingWhere{
				AssetID:        currDriverLoginSession.AssetID,
				HaulStatusCode: constants.HAUL_STATUS_CODE_READY,
				ClientID:       claim.GetLoggedInClientID(),
			},
			Preload: models.HaulingPreload{
				HaulStatus:      true,
				HaulActivity:    true,
				HaulSubActivity: true,
			},
		})
		if err != nil && !errorhandler.IsErrNotFound(err) {
			return nil, err
		}

		if latestReadyHauling != nil {
			ready := dtos.BuildHaulingResp(latestReadyHauling)
			resp.Ready = &ready
		}
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}

func (uc *HaulUseCase) GetCurrentAssetHauling(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	currentHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
		Where: models.HaulingWhere{
			AssetID:  assetID,
			IsActive: null.BoolFrom(true),
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.HaulingPreload{
			HaulStatus:                true,
			HaulActivityWithNext:      true,
			HaulActivityWithPrev:      true,
			HaulSubActivity:           true,
			CurrentDriverLoginSession: true,
			Asset:                     true,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := dtos.BuildCurrentAssetHaulingResp(currentHauling)
	if currentHauling.CurrentDriverLoginSession != nil {
		latestCanBusData, err := uc.trackingRepo.LatestCanBusSensorForHauling(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID())
		if err != nil {
			return nil, err
		}

		totalNetWeightAndTrip, err := uc.weightBridgeTicketRepo.GetTotalNetWeightAndTrip(ctx, uc.DB.DB(), assetID, claim.GetLoggedInClientID(),
			currentHauling.StartTime, time.Now())
		if err != nil {
			return nil, err
		}

		resp.BuildCurrentDriverLoginSession(currentHauling.CurrentDriverLoginSession, latestCanBusData, totalNetWeightAndTrip)
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}

func (uc *HaulUseCase) UpdateHaulingStatus(ctx context.Context, imei string, req dtos.HaulingUpdateStatusReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	currDriverLoginSession, err := uc.ValidateCurrentLogin(ctx, imei)
	if err != nil {
		return nil, err
	}

	if req.Action == constants.HAUL_STATUS_CHANGE_ACTION_CUSTOM {
		_, err := uc.haulRepo.GetHaulActivity(ctx, uc.DB.DB(), models.HaulActivityCondition{
			Where: models.HaulActivityWhere{
				Code: req.CustomHaulActivityCode,
			},
		})
		if err != nil {
			return nil, err
		}

		if req.CustomHaulSubActivityCode.Valid {
			_, err := uc.haulRepo.GetHaulSubActivity(ctx, uc.DB.DB(), models.HaulSubActivityCondition{
				Where: models.HaulSubActivityWhere{
					Code: req.CustomHaulSubActivityCode.ValueOrZero(),
				},
			})
			if err != nil {
				return nil, err
			}
		}
	}

	currentHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
		Where: models.HaulingWhere{
			AssetID:  currDriverLoginSession.AssetID,
			IsActive: null.BoolFrom(true),
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.HaulingPreload{
			HaulActivityWithNext: true,
			HaulActivityWithPrev: true,
		},
	})
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	newHauling := &models.Hauling{
		OperatorUserID:      claim.UserID,
		HaulStatusCode:      currentHauling.HaulStatusCode,
		HaulActivityCode:    "",
		HaulSubActivityCode: null.String{},
		StartTime:           time.Now(),
		AssetID:             currDriverLoginSession.AssetID,
	}

	switch req.Action {
	case constants.HAUL_STATUS_CHANGE_ACTION_NEXT:
		if currentHauling.HaulActivity.NextActivityCycle == nil {
			return nil, errorhandler.ErrBadRequest("No next activity")
		}

		newHauling.HaulActivityCode = currentHauling.HaulActivity.NextActivityCycle.NextCode
		if currentHauling.IsInLoadingActivity() {
			err := uc.haulRepo.ClearHaulDispatchLoadLocationTrip(ctx, tx.DB(), currDriverLoginSession.AssetID)
			if err != nil {
				return nil, err
			}
		} else if currentHauling.IsInDumpingActivity() {
			err := uc.haulRepo.ClearHaulDispatchDumpLocationTrip(ctx, tx.DB(), currDriverLoginSession.AssetID)
			if err != nil {
				return nil, err
			}
		}
	case constants.HAUL_STATUS_CHANGE_ACTION_PREV:
		if currentHauling.HaulActivity.PrevActivityCycle == nil {
			return nil, errorhandler.ErrBadRequest("No previous activity")
		}

		newHauling.HaulActivityCode = currentHauling.HaulActivity.PrevActivityCycle.PrevCode
	case constants.HAUL_STATUS_CHANGE_ACTION_CUSTOM:
		newHauling.HaulStatusCode = req.CustomHaulStatusCode
		newHauling.HaulActivityCode = req.CustomHaulActivityCode
		newHauling.HaulSubActivityCode = req.CustomHaulSubActivityCode
	case constants.HAUL_STATUS_CHANGE_ACTION_CONTINUE_READY:
		newHauling.HaulStatusCode = constants.HAUL_STATUS_CODE_READY
		latestReadyHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
			Where: models.HaulingWhere{
				AssetID:        currDriverLoginSession.AssetID,
				HaulStatusCode: constants.HAUL_STATUS_CODE_READY,
				ClientID:       claim.GetLoggedInClientID(),
			},
			Preload: models.HaulingPreload{
				HaulActivity: true,
			},
		})
		if err != nil && !errorhandler.IsErrNotFound(err) {
			return nil, err
		}

		if latestReadyHauling != nil {
			newHauling.HaulActivityCode = latestReadyHauling.HaulActivityCode
		} else {
			newHauling.HaulActivityCode = constants.HAUL_ACTIVITY_CODE_CHANGE_TRAVELING_TO_LOADING
		}
	}

	// End current hauling
	err = uc.haulRepo.UpdateHauling(ctx, tx.DB(), currentHauling.ID, &models.Hauling{
		EndTime: null.TimeFrom(time.Now()),
	})
	if err != nil {
		return nil, err
	}

	// Create new hauling
	err = uc.haulRepo.CreateHauling(ctx, tx.DB(), newHauling)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: currentHauling.ID,
		Data:        nil,
	}, nil
}

func (uc *HaulUseCase) UpdateAssetHaulingStatus(ctx context.Context, assetID string, req dtos.AssetHaulingUpdateStatusReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.assetRepo.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       assetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	// Check if asset is currently hauling
	currentHauling, err := uc.haulRepo.GetHauling(ctx, uc.DB.DB(), models.HaulingCondition{
		Where: models.HaulingWhere{
			AssetID:  assetID,
			IsActive: null.BoolFrom(true),
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	if currentHauling != nil {
		err = uc.haulRepo.UpdateHauling(ctx, tx.DB(), currentHauling.ID, &models.Hauling{
			EndTime: null.TimeFrom(time.Now()),
		})
		if err != nil {
			return nil, err
		}
	}

	err = uc.haulRepo.CreateHauling(ctx, tx.DB(), &models.Hauling{
		AssetID:             assetID,
		OperatorUserID:      "",
		HaulStatusCode:      req.HaulStatusCode,
		HaulActivityCode:    req.HaulActivityCode,
		HaulSubActivityCode: req.HaulSubActivityCode,
		StartTime:           time.Now(),
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	uc.SendNotifAfterUpdateStatus(ctx, assetID, req)

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        nil,
	}, nil
}

func (uc *HaulUseCase) SendNotifAfterUpdateStatus(ctx context.Context, assetID string, req dtos.AssetHaulingUpdateStatusReq) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return
	}

	currentDriverLoginSession, err := uc.haulRepo.GetDriverLoginSession(ctx, uc.DB.DB(), models.DriverLoginSessionCondition{
		Where: models.DriverLoginSessionWhere{
			AssetID:  assetID,
			IsActive: null.BoolFrom(true),
		},
	})
	if err == nil {
		userDevice, err := uc.userRepo.GetUserDevice(ctx, uc.DB.DB(), userIdentityModel.UserDeviceCondition{
			Where: userIdentityModel.UserDeviceWhere{
				UserID:         currentDriverLoginSession.UserID,
				ClientID:       claim.GetLoggedInClientID(),
				StatusCode:     userConstants.DEVICE_STATUS_CODE_ACTIVE,
				DeviceTypeCode: userConstants.DEVICE_TYPE_CODE_MOBILE_APP,
				DeviceTypeRef:  userConstants.DEVICE_TYPE_REF_DRIVER_APP_MOBILE,
			},
		})
		if err == nil {
			userClient, err := uc.userRepo.GetUserClient(ctx, uc.DB.DB(), userIdentityModel.UserClientCondition{
				Where: userIdentityModel.UserClientWhere{
					UserID:   currentDriverLoginSession.UserID,
					ClientID: claim.GetLoggedInClientID(),
				},
				Preload: userIdentityModel.UserClientPreload{
					Department: true,
				},
			})
			if err != nil {
				return
			}

			haulStatusLabel := ""
			haulActivityLabel := ""
			if req.HaulSubActivityCode.Valid && req.HaulSubActivityCode.String != "" {
				haulSubActivity, err := uc.haulRepo.GetHaulSubActivity(ctx, uc.DB.DB(), models.HaulSubActivityCondition{
					Where: models.HaulSubActivityWhere{
						Code: req.HaulSubActivityCode.String,
					},
					Preload: models.HaulSubActivityPreload{
						MainActivity: true,
						HaulStatus:   true,
					},
				})
				if err == nil {
					haulActivityLabel = haulSubActivity.MainActivity.Label + " - " + haulSubActivity.Label
					haulStatusLabel = haulSubActivity.MainActivity.HaulStatus.Label
				}
			} else {
				haulActivity, err := uc.haulRepo.GetHaulActivity(ctx, uc.DB.DB(), models.HaulActivityCondition{
					Where: models.HaulActivityWhere{
						Code: req.HaulActivityCode,
					},
					Preload: models.HaulActivityPreload{
						HaulStatus: true,
					},
				})
				if err == nil {
					haulActivityLabel = haulActivity.Label
					haulStatusLabel = haulActivity.HaulStatus.Label
				}
			}

			go sendFirebaseMsg(ctx, userDevice.FirebaseDeviceToken, map[string]string{
				"TypeCode":          "CHANGE_STATUS",
				"HaulStatusLabel":   haulStatusLabel,
				"HaulActivityLabel": haulActivityLabel,
				"DepartmentName":    userClient.Department.Name,
				"Comment":           req.Comment,
			}, "Haul Status Update", "Your haul status has been updated.")
		}
	}
}

func sendFirebaseMsg(ctx context.Context, token string, data map[string]string, title, body string) {
	_, err := firebaseApp.GetMsgClient().Send(contexthelpers.WithoutCancel(ctx), &messaging.Message{
		Token: token,
		Data:  data,
		Notification: &messaging.Notification{
			Title:    title,
			Body:     body,
			ImageURL: "https://assetfindr.com/logo192.png",
		},
	})

	if err != nil {
		commonlogger.Warnf("failed to push notif %v", err)
	}

}

func (uc *HaulUseCase) GetHaulingStatusBoard(ctx context.Context, req dtos.GetHaulingStatusBoardReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	haulings, err := uc.haulRepo.GetHaulingStatusBoard(ctx, uc.DB.DB(), models.GetHaulingStatusBoardParam{
		SearchKeyword: req.SearchKeyword,
		Cond: models.HaulingCondition{
			Where: models.HaulingWhere{
				ClientID:              claim.GetLoggedInClientID(),
				HaulStatusCodes:       req.HaulStatusCodes,
				HaulActivityCodes:     req.HaulActivityCodes,
				HaulSubActivityCodes:  req.HaulSubActivityCodes,
				AssetIDs:              req.AssetIDs,
				IsLoad:                req.IsLoad,
				IsOver12HLoginSession: req.IsOver12HLoginSession,
				IsLoadingPointEmpty:   req.IsLoadingPointEmpty,
				IsDumpingPointEmpty:   req.IsDumpingPointEmpty,
			},
			Preload: models.HaulingPreload{
				HaulStatus:                true,
				HaulActivity:              true,
				HaulSubActivity:           true,
				CurrentDriverLoginSession: true,
				Asset:                     true,
				LoadStatus:                true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		Data: dtos.BuildHaulingStatusBoardResp(haulings),
	}, nil
}

func (uc *HaulUseCase) ReportDiffKMHM(ctx context.Context, imei string, req dtos.ReportDiffKMHM) (*commonmodel.CreateResponse, error) {
	currDriverLoginSession, err := uc.ValidateCurrentLogin(ctx, imei)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	err = uc.ReportDiffKMHMWrapped(ctx, tx, currDriverLoginSession, req)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: currDriverLoginSession.AssetID,
		Data:        nil,
	}, nil
}

func (uc *HaulUseCase) ReportDiffKMHMWrapped(ctx context.Context, tx database.DBUsecase, currentDriverLoginSession *models.DriverLoginSession, req dtos.ReportDiffKMHM) error {
	dataInformation, err := uc.assetRepo.GetAssetDataInformation(ctx, uc.DB.DB(), currentDriverLoginSession.AssetID)
	if err != nil {
		return err
	}

	ticket := &taskModels.Ticket{
		Subject:              "Report Diff KMHM",
		Description:          "Report Diff KMHM",
		TicketCategoryCode:   taskConstants.TICKET_CATEGORY_CODE_OTHERS,
		TicketReferenceCode:  taskConstants.TICKET_ASSET_REF,
		ReferenceID:          currentDriverLoginSession.AssetID,
		SeverityLevelCode:    taskConstants.TICKET_SEVERITY_NOT_SET,
		RequesterUserID:      currentDriverLoginSession.UserID,
		AssignedToUserID:     null.String{},
		StatusCode:           taskConstants.TICKET_STATUS_CODE_OPEN,
		AssetDataInformation: pgtype.JSONB{Bytes: dataInformation, Status: pgtype.Present},
	}
	err = uc.ticketRepo.CreateTicket(ctx, tx.DB(), ticket)
	if err != nil {
		return err
	}
	return nil
}
