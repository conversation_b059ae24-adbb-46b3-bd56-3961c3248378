package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/app/asset/utils"
	contentRepository "assetfindr/internal/app/content/repository"
	integrationRepository "assetfindr/internal/app/integration/repository"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userModels "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"time"
)

type AssetAssignmentUseCase struct {
	DB                               database.DBUsecase
	AssetAssignmentRepository        repository.AssetAssignmentRepository
	UserRepository                   userIdentityRepository.UserRepository
	AssetLinkedRepository            repository.AssetLinkedRepository
	NotificationUsecase              notificationUsecase.NotificationUseCase
	AssetRepository                  repository.AssetRepository
	attachmentUsecase                *storageUsecase.AttachmentUseCase
	FormRepository                   contentRepository.FormRepository
	AssetVehicleRepository           repository.AssetVehicleRepository
	AssetInspectionRepository        repository.AssetInspectionRepository
	AssetInspectionTyreRepository    repository.AssetInspectionTyreRepository
	AssetInspectionVehicleRepository repository.AssetInspectionVehicleRepository
	AssetInspectionUtil              utils.AssetInspectionUtil
	AssetTyreRepository              repository.AssetTyreRepository
	integrationRepo                  integrationRepository.IntegrationRepository
}

func NewAssetAssignmentUseCase(
	DB database.DBUsecase,
	assetAssignmentRepo repository.AssetAssignmentRepository,
	userRepository userIdentityRepository.UserRepository,
	assetLinkedRepo repository.AssetLinkedRepository,
	notificationUsecase notificationUsecase.NotificationUseCase,
	assetRepository repository.AssetRepository,
	attachmentUsecase *storageUsecase.AttachmentUseCase,
	formRepo contentRepository.FormRepository,
	assetVehicleRepo repository.AssetVehicleRepository,
	assetInspectionRepo repository.AssetInspectionRepository,
	assetInspectionTyreRepo repository.AssetInspectionTyreRepository,
	assetInspectionVehicleRepo repository.AssetInspectionVehicleRepository,
	assetInspectionUtil utils.AssetInspectionUtil,
	assetTyreRepo repository.AssetTyreRepository,
	integrationRepo integrationRepository.IntegrationRepository,
) *AssetAssignmentUseCase {
	return &AssetAssignmentUseCase{
		DB:                               DB,
		AssetAssignmentRepository:        assetAssignmentRepo,
		UserRepository:                   userRepository,
		AssetLinkedRepository:            assetLinkedRepo,
		NotificationUsecase:              notificationUsecase,
		AssetRepository:                  assetRepository,
		attachmentUsecase:                attachmentUsecase,
		FormRepository:                   formRepo,
		AssetVehicleRepository:           assetVehicleRepo,
		AssetInspectionRepository:        assetInspectionRepo,
		AssetInspectionTyreRepository:    assetInspectionTyreRepo,
		AssetInspectionVehicleRepository: assetInspectionVehicleRepo,
		AssetInspectionUtil:              assetInspectionUtil,
		AssetTyreRepository:              assetTyreRepo,
		integrationRepo:                  integrationRepo,
	}
}

func (uc *AssetAssignmentUseCase) GetAssetAssignmentByAssetIds(ctx context.Context, assetAssignmentsMapByAssetId *map[string]dtos.AssetAssignmentResponse, assetIds []string) error {

	var assetAssignments []models.AssetAssignment

	err := uc.AssetAssignmentRepository.GetAssetAssignmentByAssetIds(ctx, uc.DB.DB(), &assetAssignments, assetIds)
	if err != nil {
		commonlogger.Errorf("Error in getting asset assignment by asset ids", err)
		return err
	}

	var userIds []string
	for _, assignment := range assetAssignments {
		userIds = append(userIds, assignment.UserID)
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return err
	}

	responses := dtos.BuildAssetAssignmentResponse(assetAssignments, usersMapById)

	// Ensure 'assetAssignmentsMapByAssetId' is initialized before storing users
	if *assetAssignmentsMapByAssetId == nil {
		*assetAssignmentsMapByAssetId = make(map[string]dtos.AssetAssignmentResponse)
	}

	for _, assetAssignment := range responses {
		(*assetAssignmentsMapByAssetId)[assetAssignment.AssetID] = assetAssignment
	}

	return nil
}

func (uc *AssetAssignmentUseCase) CreateAssetAssignmentV2(ctx context.Context, dB database.DBI, assetAssignment *models.AssetAssignment) error {
	err := uc.AssetAssignmentRepository.CreateAssetAssignment(ctx, dB, assetAssignment)
	if err != nil {
		return err
	}

	go uc.notifyAfterAssetAssignment(contexthelpers.WithoutCancel(ctx), assetAssignment.AssetID)
	return nil
}

func (uc *AssetAssignmentUseCase) GetAssetAssignmentByAssetId(ctx context.Context, assetAssignment *models.AssetAssignment, assetId string) error {
	return uc.AssetAssignmentRepository.GetAssetAssignmentByAssetId(ctx, uc.DB.DB(), assetAssignment, assetId)
}

func (uc *AssetAssignmentUseCase) GetAssetAssignmentResponseByAssetId(ctx context.Context, assetAssignment *dtos.AssetAssignmentResponse, assetId string) error {
	var assignment models.AssetAssignment
	err := uc.AssetAssignmentRepository.GetAssetAssignmentByAssetId(ctx, uc.DB.DB(), &assignment, assetId)
	if err != nil {
		return err
	}

	var assignedUser userIdentityModel.User
	if assignment.UserID != "" {
		err = uc.UserRepository.GetUserById(ctx, uc.DB.DB(), &assignedUser, assignment.UserID)
		if err != nil {
			commonlogger.Errorf("Error in getting users by user id from identity service", err)
			return err
		}
	}

	assetAssignment.ID = assignment.ID
	assetAssignment.AssetID = assignment.AssetID
	assetAssignment.UserID = assignment.UserID
	assetAssignment.AssignedDateTime = assignment.AssignedDateTime
	assetAssignment.UnassignedDateTime = assignment.UnassignedDateTime
	assetAssignment.ClientID = assignment.ClientID
	assetAssignment.AssignedToUserFullName = assignedUser.FirstName + " " + assignedUser.LastName

	return nil
}

func (uc *AssetAssignmentUseCase) UnAssignAssetAssignmentByAssetId(ctx context.Context, assetId string) error {
	return uc.AssetAssignmentRepository.UnAssignAssetAssignmentByAssetId(ctx, uc.DB.DB(), assetId)
}

func (uc *AssetAssignmentUseCase) ReassignAssetAssignment(ctx context.Context, assetId string, userId string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	/*
		Check whether the assignment already exist with the same user id
	*/
	currentAssetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(
		ctx, uc.DB.DB(), models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  assetId,
				Assigned: true,
			},
		},
	)

	if err != nil && !errorhandler.IsErrNotFound(err) {
		return err
	}

	if currentAssetAssignment != nil && currentAssetAssignment.UserID == userId {
		return nil
	}

	/*
		Reassigned all asset which attached to the asset
	*/
	assetChildLinkeds, err := uc.AssetLinkedRepository.GetAssetLinkeds(
		ctx, uc.DB.DB(), models.AssetLinkedCondition{
			Where: models.AssetLinkedWhere{
				ParentAssetID: assetId,
			},
		},
	)
	if err != nil {
		return err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	defer tx.Rollback()

	assetIDs := make([]string, 0, len(assetChildLinkeds)+1)
	assetIDs = append(assetIDs, assetId)
	for _, assetLinked := range assetChildLinkeds {
		assetIDs = append(assetIDs, assetLinked.ChildAssetID)
	}

	err = uc.AssetAssignmentRepository.UnAssignAssetAssignmentByAssetIDs(ctx, tx.DB(), assetIDs, claim.UserID)
	if err != nil {
		return err
	}

	now := time.Now().In(time.UTC)
	assetAssignments := make([]models.AssetAssignment, 0, len(assetChildLinkeds)+1)
	assetAssignments = append(assetAssignments, models.AssetAssignment{
		AssetID:            assetId,
		UserID:             userId,
		AssignedDateTime:   now,
		UnassignedDateTime: nil,
		ClientID:           claim.GetLoggedInClientID(),
		AssignedByUserID:   claim.UserID,
	})

	for _, assetChildLinked := range assetChildLinkeds {
		assetAssignments = append(assetAssignments, models.AssetAssignment{
			AssetID:          assetChildLinked.ChildAssetID,
			UserID:           userId,
			AssignedDateTime: now,
			ClientID:         claim.GetLoggedInClientID(),
			AssignedByUserID: claim.UserID,
		})
	}

	err = uc.AssetAssignmentRepository.CreateAssetAssignments(ctx, tx.DB(), assetAssignments)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	go uc.notifyAfterAssetAssignment(contexthelpers.WithoutCancel(ctx), assetId)
	return nil
}

func (uc *AssetAssignmentUseCase) notifyAfterAssetAssignment(
	ctx context.Context,
	assetID string,
) {
	assetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(
		ctx, uc.DB.DB(), models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  assetID,
				Assigned: true,
			},
			Preload: models.AssetAssignmentPreload{
				Asset:      true,
				AssetBrand: true,
			},
		})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get asset assignment %v", err)
		}
		return
	}

	// Currently only notify on assign vehicle
	if assetAssignment.Asset.AssetCategoryCode != constants.ASSET_CATEGORY_VEHICLE_CODE {
		return
	}

	title, err := tmplhelpers.ParseStringTemplate(
		"Vehicle {{.vehicleName}} /. {{.serialNumber}} has been assigned to you",
		map[string]interface{}{
			"vehicleName":  assetAssignment.Asset.Name,
			"serialNumber": assetAssignment.Asset.SerialNumber,
		})
	if err != nil {
		commonlogger.Warnf("failed to create title %v", err)
		return
	}

	body, err := tmplhelpers.ParseStringTemplate(
		title+`<table>
		{{ range $key, $value := . }}
			<tr>
			<td>{{ $key }}</td>: <td>{{ $value }}</td>
			</tr>
		{{ end }}
		</table>
		<br>`,
		map[string]interface{}{
			"New Status":    assetAssignment.Asset.StatusLabel(),
			"Serial Number": assetAssignment.Asset.SerialNumber,
			"Brand Name":    assetAssignment.Asset.Brand.BrandName,
			// "Model":         assetVehicle..PatternType,
		},
	)
	if err != nil {
		commonlogger.Warnf("failed to create assetVehicleTable for email %v", err)
		return
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetAssignment.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_ASSIGNMENT,
		SourceReferenceID: assetAssignment.AssetID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          assetAssignment.ClientID,
		TypeCode:          "",
		ContentTypeCode:   "",
		ReferenceCode:     notifConstants.NOTIF_REF_ASSET,
		ReferenceValue:    assetID,
	}

	_ = uc.NotificationUsecase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *AssetAssignmentUseCase) UnassignAssetAssignment(ctx context.Context, assetId string, remark string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	assetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(
		ctx, uc.DB.DB(), models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  assetId,
				Assigned: true,
			},
			Preload: models.AssetAssignmentPreload{
				Asset:      true,
				AssetBrand: true,
			},
		})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get asset assignment %v", err)
		}
		return err
	}

	/*
		Reassigned all asset which attached to the asset
	*/
	assetChildLinkeds, err := uc.AssetLinkedRepository.GetAssetLinkeds(
		ctx, uc.DB.DB(), models.AssetLinkedCondition{
			Where: models.AssetLinkedWhere{
				ParentAssetID: assetId,
			},
		},
	)
	if err != nil {
		return err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	defer tx.Rollback()

	assetIDs := make([]string, 0, len(assetChildLinkeds)+1)
	assetIDs = append(assetIDs, assetId)
	for _, assetLinked := range assetChildLinkeds {
		assetIDs = append(assetIDs, assetLinked.ChildAssetID)
	}

	err = uc.AssetAssignmentRepository.UnAssignAssetAssignmentByAssetIDs(ctx, tx.DB(), assetIDs, claim.UserID)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	// Currently only notify on assign vehicle
	go uc.notifyAfterAssetUnassignment(contexthelpers.WithoutCancel(ctx), assetAssignment, remark)

	return nil
}

func (uc *AssetAssignmentUseCase) notifyAfterAssetUnassignment(
	ctx context.Context,
	assetAssignment *models.AssetAssignment,
	remark string,
) {
	var idCredential = assetAssignment.Asset.ReferenceNumber
	if idCredential == "" {
		idCredential = assetAssignment.Asset.SerialNumber
	}
	var title = "Asset has been Unassigned"

	body, err := tmplhelpers.ParseStringTemplate(
		"You have been unassigned from {{.idCredential}} with the reason {{.remark}}. ",
		map[string]interface{}{
			"idCredential": idCredential,
			"remark":       remark,
		})
	if err != nil {
		commonlogger.Warnf("failed to create body %v", err)
		return
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetAssignment.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_ASSIGNMENT,
		SourceReferenceID: assetAssignment.AssetID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          assetAssignment.ClientID,
		TypeCode:          "",
		ContentTypeCode:   "",
		ReferenceCode:     notifConstants.NOTIF_REF_ASSET,
		ReferenceValue:    assetAssignment.AssetID,
	}

	_ = uc.NotificationUsecase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *AssetAssignmentUseCase) GetAssetAssignments(ctx context.Context, req dtos.AssetAssignmentListReq) (commonmodel.ListResponse, error) {
	assetAssignmentsResponse := commonmodel.ListResponse{}

	clientId, err := authhelpers.GetClaimClientIdFromCtx(ctx)
	if err != nil {
		return assetAssignmentsResponse, err
	}

	totalRecords, assetAssignments, err := uc.AssetAssignmentRepository.GetAssetAssignmentList(ctx, uc.DB.DB(), models.GetAssetAssignmentListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  req.AssetId,
				ClientID: clientId,
			},
		},
	})
	if err != nil {
		return assetAssignmentsResponse, err
	}

	if len(assetAssignments) == 0 {
		return assetAssignmentsResponse, nil
	}

	mapUserName := map[string]string{}
	userIDs := []string{}
	for i := range assetAssignments {
		if assetAssignments[i].AssignedByUserID != "" {
			userIDs = append(userIDs, assetAssignments[i].AssignedByUserID)
		}

		if assetAssignments[i].UnassignedByUserID != "" {
			userIDs = append(userIDs, assetAssignments[i].UnassignedByUserID)
		}

		if assetAssignments[i].UserID != "" {
			userIDs = append(userIDs, assetAssignments[i].UserID)
		}
	}

	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return assetAssignmentsResponse, err
		}

		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	newResponse := []dtos.AssetAssignmentListItemResponse{}
	for i := range assetAssignments {
		newResponse = append(newResponse, dtos.BuildAssetAssignmentListRespItem(assetAssignments[i], mapUserName))
	}

	assetAssignmentsResponse = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         newResponse,
	}

	return assetAssignmentsResponse, nil
}

func (uc *AssetAssignmentUseCase) ReassignAssetAssignmentV2(ctx context.Context, tx database.DBUsecase, assetId string, userId string, requesterUserId string) error {
	clientId, err := authhelpers.GetClaimClientIdFromCtx(ctx)
	if err != nil {
		return err
	}

	/*
		Check whether the assignment already exist with the same user id
	*/
	currentAssetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(
		ctx, uc.DB.DB(), models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  assetId,
				Assigned: true,
			},
		},
	)

	if err != nil && !errorhandler.IsErrNotFound(err) {
		return err
	}

	if currentAssetAssignment != nil && currentAssetAssignment.UserID == userId {
		return nil
	}

	assetChildLinkeds, err := uc.AssetLinkedRepository.GetAssetLinkeds(
		ctx, uc.DB.DB(), models.AssetLinkedCondition{
			Where: models.AssetLinkedWhere{
				ParentAssetID: assetId,
			},
		},
	)
	if err != nil {
		return err
	}

	assetIDs := make([]string, 0, len(assetChildLinkeds)+1)
	assetIDs = append(assetIDs, assetId)
	for _, assetLinked := range assetChildLinkeds {
		assetIDs = append(assetIDs, assetLinked.ChildAssetID)
	}

	err = uc.AssetAssignmentRepository.UnAssignAssetAssignmentByAssetIDs(ctx, tx.DB(), assetIDs, requesterUserId)
	if err != nil {
		return err
	}

	now := time.Now().In(time.UTC)

	err = uc.AssetAssignmentRepository.CreateAssetAssignment(ctx, tx.DB(), &models.AssetAssignment{
		AssetID:            assetId,
		UserID:             userId,
		AssignedDateTime:   now,
		UnassignedDateTime: nil,
		ClientID:           clientId,
		AssignedByUserID:   requesterUserId,
	})
	if err != nil {
		return err
	}

	// go uc.notifyAfterAssetAssignment(contexthelpers.WithoutCancel(ctx), assetId)
	return nil
}

func (uc *AssetAssignmentUseCase) GetAssetAssignmentAndGroups(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	assetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(ctx, uc.DB.DB(), models.AssetAssignmentCondition{
		Where: models.AssetAssignmentWhere{
			AssetID:  assetID,
			Assigned: true,
		},
		Columns: []string{"user_id"},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	notUserID := ""
	if assetAssignment != nil {
		notUserID = assetAssignment.UserID
	}

	assetAssignmentGroups, err := uc.AssetAssignmentRepository.GetAssetAssignmentGroup(ctx, uc.DB.DB(), models.AssetAssignmentCondition{
		Where: models.AssetAssignmentWhere{
			AssetID:   assetID,
			NotUserID: notUserID,
		},
		Columns: []string{"user_id"},
	})
	if err != nil {
		return nil, err
	}

	userIDs := make([]string, 0, len(assetAssignmentGroups)+1)

	if assetAssignment != nil {
		userIDs = append(userIDs, assetAssignment.UserID)
	}
	for _, assetAssignmentGroup := range assetAssignmentGroups {
		userIDs = append(userIDs, assetAssignmentGroup.UserID)
	}

	if len(userIDs) == 0 {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        []dtos.GetAssetAssignmentsByDeviceRespItem{},
		}, nil
	}

	users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
		Where: userModels.UserWhere{
			IDs: userIDs,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := []dtos.GetAssetAssignmentsByDeviceRespItem{}
	for _, user := range users {
		resp = append(resp, dtos.GetAssetAssignmentsByDeviceRespItem{
			UserName:    user.GetName(),
			UserID:      user.ID,
			ReferenceID: user.ReferenceID,
		})
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        resp,
	}, nil
}
