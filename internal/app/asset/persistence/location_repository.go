package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"strconv"

	"gorm.io/gorm"
)

type locationRepository struct{}

func NewLocationRepository() repository.LocationRepository {
	return &locationRepository{}
}

func (r *locationRepository) CreateLocation(ctx context.Context, dB database.DBI, loc *models.Location) error {
	return dB.GetTx().Create(loc).Error
}

func enrichLocationQueryWithWhere(query *gorm.DB, where models.LocationWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if !where.ShowDeleted {
		query.Where("status_code != ?", constants.LOCATION_STATUS_CODE_DELETED)
	} // ShowDeleted

	if where.WithOrmDeleted {
		query.Unscoped()
	}

	if len(where.StatusCodes) > 0 {
		query.Where("status_code in ?", where.StatusCodes)
	} // StatusCodes

	if len(where.LocationTypeCodes) > 0 {
		query.Where("location_type_code in ?", where.LocationTypeCodes)
	} // LocationTypes
}

func enrichLocationRouteQueryWithWhere(query *gorm.DB, where models.LocationRouteWhere) {
	if where.ID != "" {
		query.Where("ams_location_routes.id = ?", where.ID)
	} // ID

	if len(where.ExcludedIDs) > 0 {
		query.Where("ams_location_routes.id NOT IN ?", where.ExcludedIDs)
	} // ExcludedIDs

	if where.ClientID != "" {
		query.Where("ams_location_routes.client_id = ?", where.ClientID)
	} // ClientID

	if where.StartLocationID != "" {
		query.Where("ams_location_routes.start_location_id = ?", where.StartLocationID)
	} // StartLocationID

	if where.EndLocationID != "" {
		query.Where("ams_location_routes.end_location_id = ?", where.EndLocationID)
	} // EndLocationID

	if where.EndLocationChildID != "" {
		query.Joins("INNER JOIN ams_locations ams_locations ON ams_location_routes.end_location_id=ams_locations.parent_location_id")
		query.Where("ams_locations.id = ?", where.EndLocationChildID)
	} // EndLocationChildID

	if where.LocationID != "" {
		query.Where("ams_location_routes.start_location_id = ? OR ams_location_routes.end_location_id = ?", where.LocationID, where.LocationID)
	}
}

func enrichLocationQueryWithPreload(query *gorm.DB, where models.LocationRoutePreload) {
	if where.EndLocation {
		query.Preload("EndLocation")
	} // EndLocation

	if where.StartLocation {
		query.Preload("StartLocation")
	} // StartLocation
}

func (r *locationRepository) GetLocation(ctx context.Context, dB database.DBI, cond models.LocationCondition) (*models.Location, error) {
	location := models.Location{}
	query := dB.GetOrm().Model(&location)

	enrichLocationQueryWithWhere(query, cond.Where)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&location).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("location")
		}

		return nil, err
	}

	return &location, nil
}

func (r *locationRepository) GetLocationList(ctx context.Context, dB database.DBI, param models.GetLocationListParam) (int, []models.Location, error) {
	var totalRecords int64
	locs := []models.Location{}
	query := dB.GetTx().Model(&locs)
	enrichLocationQueryWithWhere(query, param.Cond.Where)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().Where("LOWER(name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(address) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&locs).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), locs, nil

}

func (r *locationRepository) UpdateLocation(ctx context.Context, dB database.DBI, id string, loc *models.Location) error {
	return dB.GetTx().
		Model(&models.Location{}).
		Where("id = ?", id).
		Updates(loc).
		Error
}

func (r *locationRepository) GetLocationByIds(ctx context.Context, dB database.DBI, locations *[]models.Location, locationIds []string) error {
	if err := dB.GetOrm().Where("ID IN ?", locationIds).Find(locations).Error; err != nil {
		return err
	}
	return nil
}

func (r *locationRepository) GetLocationRouteList(ctx context.Context, dB database.DBI, param models.GetLocationRouteListParam) (int, []models.LocationRoute, error) {
	var totalRecords int64
	locRoutes := []models.LocationRoute{}
	query := dB.GetTx().Model(&locRoutes)
	query.Joins("LEFT JOIN ams_locations AS start_locations ON start_locations.id=ams_location_routes.start_location_id").
		Joins("LEFT JOIN ams_locations AS end_locations ON end_locations.id=ams_location_routes.end_location_id")
	enrichLocationRouteQueryWithWhere(query, param.Cond.Where)
	enrichLocationQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		searchKeyword := param.SearchKeyword

		subQuery := query.Session(&gorm.Session{NewDB: true}).Unscoped().
			Where("LOWER(start_locations.name) LIKE LOWER(?)", "%"+searchKeyword+"%").
			Or("LOWER(end_locations.name) LIKE LOWER(?)", "%"+searchKeyword+"%")

		if num, err := strconv.ParseFloat(searchKeyword, 64); err == nil {
			subQuery = subQuery.Or("ams_location_routes.distance_km = ?", num)
		}

		query.Where(subQuery)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("ams_location_routes.updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&locRoutes).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), locRoutes, nil
}

func updateIsLatestCombination(dB database.DBI) error {
	err := dB.GetTx().Exec(`
		WITH latest AS (
			SELECT DISTINCT ON (start_location_id, end_location_id) id
			FROM ams_location_routes
			ORDER BY start_location_id, end_location_id, updated_at DESC
		)
		UPDATE ams_location_routes
		SET is_latest_combination = (id IN (SELECT id FROM latest))
	`).Error
	if err != nil {
		return err
	}

	return nil
}

func (r *locationRepository) CreateLocationRoute(ctx context.Context, dB database.DBI, locRoute *models.LocationRoute) error {
	if err := dB.GetTx().Create(locRoute).Error; err != nil {
		return err
	}

	if err := updateIsLatestCombination(dB); err != nil {
		return err
	}

	return nil
}

func (r *locationRepository) GetLocationRoute(ctx context.Context, dB database.DBI, cond models.LocationRouteCondition) (*models.LocationRoute, error) {
	locationRoute := models.LocationRoute{}
	query := dB.GetOrm().Model(&locationRoute)

	enrichLocationRouteQueryWithWhere(query, cond.Where)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Order("updated_at DESC").First(&locationRoute).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("location route")
		}

		return nil, err
	}

	return &locationRoute, nil
}

func (r *locationRepository) UpdateLocationRoute(ctx context.Context, dB database.DBI, id string, locRoute *models.LocationRoute) error {
	if err := dB.GetTx().
		Model(&models.LocationRoute{}).
		Where("id = ?", id).
		Select("*").
		Updates(locRoute).
		Error; err != nil {
		return err
	}

	if err := updateIsLatestCombination(dB); err != nil {
		return err
	}

	return nil
}

func (r *locationRepository) DeleteLocationRoutes(ctx context.Context, dB database.DBI, ids []string, clientID string) error {
	if len(ids) == 0 {
		return nil
	}
	return dB.GetTx().
		Where("id IN ?", ids).
		Where("client_id = ?", clientID).
		Delete(&models.LocationRoute{}).
		Error
}
