package usecase

import (
	"assetfindr/cmd/flespimqttservice/model"
	assetConstants "assetfindr/internal/app/asset/constants"
	assetModel "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/geo/dtos"
	"assetfindr/internal/app/geo/models"
	integrationConstants "assetfindr/internal/app/integration/constants"
	integrationModel "assetfindr/internal/app/integration/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/helpers/maphelpers"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"context"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

func (uc *TrackingUseCase) ProcessIntegrationsDataMapping(
	ctx context.Context,
	incomingDataBytes []byte,
	isFromFlespiProtocol bool,
) error {

	commonlogger.Infof(string(incomingDataBytes))
	var reqIdent dtos.InsertSensorDataIdentReq
	err := json.Unmarshal(incomingDataBytes, &reqIdent)
	if err != nil {
		uc.CreateLogRawSensorDatalake(string(incomingDataBytes))
		return err
	}

	if reqIdent.PayloadText.String != "" {
		defer uc.InsertBulkTyreSensorDataV2(ctx, reqIdent.PayloadText.String, reqIdent.Timestamp)
	}

	integrations, err := uc.GetIntegrationsForInsertData(ctx, reqIdent, incomingDataBytes)
	if err != nil {
		return err
	}

	for i := range integrations {
		err := uc.ProcessIntegrationDataMapping(ctx, &integrations[i], incomingDataBytes, processIntegrationDataMappingParam{
			isFromFlespiProtocol: isFromFlespiProtocol,
			customTimestamp:      null.Float{},
			IsNeedCheckSubSensor: true,
		})
		if err != nil && !errorhandler.IsErrNotFound(err) {
			commonlogger.Errorf("falied to process integration data mapping data err: %v, incoming data: %s", err, string(incomingDataBytes))
		}
	}

	return nil
}

type processIntegrationDataMappingParam struct {
	isFromFlespiProtocol bool
	customTimestamp      null.Float
	IsNeedCheckSubSensor bool
}

func (uc *TrackingUseCase) UpsertLatestIntegrationData(ctx context.Context, integration *integrationModel.Integration, req dtos.InsertSensorDataReq) {
	reqDataJSONBValue := pgtype.JSONB{}
	reqDataJSON, err := json.Marshal(req)
	if err != nil {
		commonlogger.Errorf("failed to marshal req dtos, err: %v", err)
	}
	err = reqDataJSONBValue.Set(reqDataJSON)
	if err != nil {
		commonlogger.Errorf("failed to set JSONB value, err:%v", err)
		return
	}
	latestIntegrationData := models.LatestIntegrationData{
		IntegrationID: integration.ID,
		DataJSON:      reqDataJSONBValue,
		ClientID:      integration.ClientID,
		UpdatedAt:     time.Now(),
	}
	err = uc.trackingRepo.UpsertLatestIntegrationData(ctx, uc.DBTimeScale.DB(), &latestIntegrationData)
	if err != nil {
		commonlogger.Errorf("failed to upsert latest integration data, err: %v", err)
	}
}

func (uc *TrackingUseCase) ProcessIntegrationDataMapping(
	ctx context.Context,
	integration *integrationModel.Integration,
	incomingDataBytes []byte,
	param processIntegrationDataMappingParam,
) error {
	mappingData := integration.CustomDataMapping
	if integration.DataMappingCode != integrationConstants.INTEGRATION_DATA_MAPPING_TEMPLATE_CUSTOM_CODE {
		mappingData = integration.DataMapping.DataMapping
	}

	mappingDataMap, err := maphelpers.JSONBToMapStringToString(mappingData)
	if err != nil {
		return err
	}

	var incomingData map[string]interface{}
	err = json.Unmarshal(incomingDataBytes, &incomingData)
	if err != nil {

		return err
	}

	incomingDataFlat := maphelpers.FlattenMap(incomingData)

	result := map[string]interface{}{}
	manualCanBusData := map[string]string{}
	hasManualCanBusData := false
	for key, value := range mappingDataMap {
		if incomingDataFlat[value] == nil {
			continue
		}

		if strings.HasPrefix(key, integrationConstants.CAN_MANUAL_PREFIX) {
			stringVal, ok := incomingDataFlat[value].(string)
			if !ok {
				commonlogger.Warnf("some manual can data is can be parsed, value type is: %t", incomingDataFlat[value])
			} else {
				hasManualCanBusData = true
				manualCanBusData[key] = stringVal
			}
		} else {
			result[key] = incomingDataFlat[value]
		}
	}

	if param.customTimestamp.Valid {
		result["time"] = param.customTimestamp.Float64
	}

	jsonData, err := json.Marshal(result)
	if err != nil {
		return err
	}

	var req dtos.InsertSensorDataReq
	err = json.Unmarshal(jsonData, &req)
	if err != nil {
		return err
	}

	req.CanManualData = manualCanBusData
	req.HasCanManualData = hasManualCanBusData
	uc.InsertSensorData(ctx, integration, req, result)

	if param.isFromFlespiProtocol {
		_ = uc.trackingRepo.CreateFlespiSensorDataLake(ctx, uc.BQ.Conn(), &model.FlespiData{
			AssetId:   integration.InternalReferenceID,
			Time:      time.Unix(int64(req.Time), 0).In(time.UTC),
			Ident:     req.Ident,
			Data:      string(incomingDataBytes),
			CreatedAt: time.Now().In(time.UTC),
		})
	}

	tm := time.Unix(int64(req.Time), 0).In(time.UTC)
	_ = uc.trackingRepo.CreateLogRawSensorDataLake(ctx, uc.BQ.Conn(), &models.LogRawSensorData{
		Time:       null.NewTime(tm, !tm.IsZero()),
		Ident:      req.Ident,
		CreatedAt:  time.Now().UTC(),
		Data:       string(incomingDataBytes),
		SourceCode: integration.IntegrationTargetTypeCode,
	})

	if param.IsNeedCheckSubSensor {
		uc.InsertBulkTyreSensorData(ctx, incomingData, req.Time)
	}

	return nil
}

func (uc *TrackingUseCase) GetIntegrationsForInsertData(ctx context.Context, reqIdent dtos.InsertSensorDataIdentReq, incomingDataBytes []byte) ([]integrationModel.Integration, error) {
	ident, tag, possibleSource, err := reqIdent.GetIdentAndTag()
	if err != nil {
		uc.CreateLogRawSensorDatalake(string(incomingDataBytes))
		return nil, err
	}

	integrations, err := uc.integrationRepo.GetIntegrations(ctx, uc.DB.DB(), integrationModel.IntegrationCondition{
		Where: integrationModel.IntegrationWhere{
			IdentifierJSON: map[string]string{
				tag: ident,
			},
			Status: integrationConstants.INTEGRATION_STATUS_CODE_ACTIVE,
		},
		Preload: integrationModel.IntegrationPreload{
			DataMapping: true,
		},
	})

	if err != nil {
		_ = uc.trackingRepo.CreateLogRawSensorDataLake(ctx, uc.BQ.Conn(), &models.LogRawSensorData{
			Time:       null.Time{},
			Ident:      ident,
			CreatedAt:  time.Now().UTC(),
			Data:       string(incomingDataBytes),
			SourceCode: possibleSource,
		})
		return nil, err
	}

	return integrations, nil
}

func (uc *TrackingUseCase) InsertBulkTyreSensorData(ctx context.Context, incomingDataFlat map[string]interface{}, timestamp float64) {
	for i := 1; i < 20; i++ {
		base64EncodedVal, ok := incomingDataFlat["cp"+strconv.Itoa(i)]
		if !ok {
			continue
		}

		base64EncodedValString, ok := base64EncodedVal.(string)
		if !ok {
			continue
		}

		decodedVal, err := base64.StdEncoding.DecodeString(base64EncodedValString)
		if err != nil {
			continue
		}

		var reqIdent dtos.InsertSubSensorDataIdentReq
		err = json.Unmarshal(decodedVal, &reqIdent)
		if err != nil {
			commonlogger.Errorf("falied to parse json data err: %v, incoming data: %s", err, string(decodedVal))
			continue
		}

		ident, tag, possibleSource, err := reqIdent.GetIdentAndTag()
		if err != nil {
			continue
		}

		integrations, err := uc.integrationRepo.GetIntegrations(ctx, uc.DB.DB(), integrationModel.IntegrationCondition{
			Where: integrationModel.IntegrationWhere{
				IdentifierJSON: map[string]string{
					tag: ident,
				},
				Status: integrationConstants.INTEGRATION_STATUS_CODE_ACTIVE,
			},
			Preload: integrationModel.IntegrationPreload{
				DataMapping: true,
			},
		})
		if err != nil {
			_ = uc.trackingRepo.CreateLogRawSensorDataLake(ctx, uc.BQ.Conn(), &models.LogRawSensorData{
				Time:       null.Time{},
				Ident:      ident,
				CreatedAt:  time.Now().UTC(),
				Data:       string(decodedVal),
				SourceCode: possibleSource,
			})
			continue
		}

		for _, integration := range integrations {
			err = uc.ProcessIntegrationDataMapping(ctx, &integration, decodedVal, processIntegrationDataMappingParam{
				isFromFlespiProtocol: false,
				customTimestamp:      null.FloatFrom(timestamp),
				IsNeedCheckSubSensor: false,
			})
			if err != nil {
				commonlogger.Errorf("falied to process integration data mapping data tyre sensor err: %v, incoming data: %s", err, string(decodedVal))
				continue
			}
		}

	}
}

// Insert Using `payload.text` flespi`
func (uc *TrackingUseCase) InsertBulkTyreSensorDataV2(ctx context.Context, payloadText string, timestamp float64) {
	payloads := strings.Split(payloadText, " ")

	logRawData := []*models.LogRawSensorData{}

	for _, payload := range payloads {
		uc.InsertTyreSensorDataV2(ctx, payload, timestamp)

		if len(payload) >= 12 {
			logRawData = append(logRawData, &models.LogRawSensorData{
				Ident:      payload[:12],
				Data:       fmt.Sprintf(`{"string": %s}`, strconv.Quote(payload)),
				Time:       null.TimeFrom(time.Unix(int64(timestamp), 0).In(time.UTC)),
				SourceCode: integrationConstants.TRACKING_FLESPI,
				CreatedAt:  time.Now().In(time.UTC),
			})
		}
	}

	uc.trackingRepo.CreateLogRawSensorDatasLake(ctx, uc.BQ.Conn(), logRawData)
}

func (uc *TrackingUseCase) InsertTyreSensorDataV2(ctx context.Context, payload string, timestamp float64) {
	if len(payload) < 43 {
		return
	}

	// a4c13812e36a,-10,190206005002480025049e0c08fffa0000ffffffffffffffff1a
	//                  0123456789012345678901234567890

	splitPayload := strings.Split(payload, ",")
	if len(splitPayload) < 3 {
		return
	}

	ident := splitPayload[0]

	integrations, err := uc.integrationRepo.GetIntegrations(ctx, uc.DB.DB(), integrationModel.IntegrationCondition{
		Where: integrationModel.IntegrationWhere{
			IdentifierJSON: map[string]string{
				"tyre_sensor_id": ident,
			},
		},
	})
	if err != nil {
		commonlogger.Warnf("get integration err: %v, payload: %s", err, payload)
		return
	}

	req := dtos.InsertSensorDataReq{
		Ident:               ident,
		Time:                timestamp,
		TyreIdentMacAddress: null.StringFrom(ident),
	}

	tempHex := splitPayload[2][14:18]

	temp, err := strconv.ParseInt(tempHex, 16, 64)
	if err != nil {
		commonlogger.Errorf("falied to parse tyre sensor temp data err: %v, payload: %s", err, payload)
		return
	}

	req.TyreTemperature = null.FloatFrom(float64(temp))
	pressHex := splitPayload[2][18:22]

	pressX10, err := strconv.ParseInt(pressHex, 16, 64)
	if err != nil {
		commonlogger.Errorf("falied to parse tyre sensor press data err: %v, payload: %s", err, payload)
		return
	}

	req.TyrePressure = null.FloatFrom(float64(pressX10-100) * 0.145038)
	voltHex := splitPayload[2][22:26]
	milliVolt, err := strconv.ParseInt(voltHex, 16, 64)
	if err != nil {
		commonlogger.Errorf("falied to parse tyre sensor volt data err: %v, payload: %s", err, payload)
		return
	}

	req.TyreBatteryVoltage = null.FloatFrom(float64(milliVolt) / 1000)
	reqMap := map[string]interface{}{
		"tyre.temperature":     req.TyreTemperature.Float64,
		"tyre.pressure":        req.TyrePressure.Float64,
		"tyre.battery_voltage": req.TyreBatteryVoltage.Float64,
	}

	// Add battery_percent to reqMap if it's valid
	if req.TyreBatteryPercent.Valid {
		reqMap["tyre.battery_percent"] = req.TyreBatteryPercent.Float64
	}

	for _, integration := range integrations {
		uc.InsertSensorData(ctx, &integration, req, reqMap)
	}
}

func (uc *TrackingUseCase) InsertSensorData(ctx context.Context, integration *integrationModel.Integration, req dtos.InsertSensorDataReq, reqMap map[string]interface{}) {
	doUpsertLatestIntegrationData := false
	if req.IsCompressorDataValid() {
		compressorData := generateCompressorDataFromReq(req, integration)
		err := uc.trackingRepo.CreateCompressorSensor(ctx, uc.DBTimeScale.DB(), &compressorData)
		if err != nil {
			commonlogger.Errorf("falied to save compressor data err: %v", err)
		}

		err = uc.trackingRepo.CreateCompressorSensorDataLake(ctx, uc.BQ.Conn(), &compressorData)
		if err != nil {
			commonlogger.Errorf("falied to save compressor data to big query err: %v", err)
		}

		doUpsertLatestIntegrationData = true
	}

	if req.IsCanBusDataValid() {
		canBusData := uc.generateCanBusDataFromReq(req, reqMap, integration)
		if canBusData.IsValid() {
			err := uc.trackingRepo.CreateCanBusSensor(ctx, uc.DBTimeScale.DB(), &canBusData)
			if err != nil {
				commonlogger.Errorf("falied to save can bus data err: %v", err)
			}

			err = uc.trackingRepo.CreateCanBusSensorDataLake(ctx, uc.BQ.Conn(), &canBusData)
			if err != nil {
				commonlogger.Errorf("falied to save can bus data to big query err: %v", err)
			}

			doUpsertLatestIntegrationData = true
		}

	}

	if req.IsGpsDataValid() {
		trackingData := generateGpsTrackingFromReq(req, integration)
		err := uc.trackingRepo.CreateTrackings(ctx, uc.DBTimeScale.DB(), []models.Tracking{trackingData})
		if err != nil {
			commonlogger.Errorf("falied to save gps data err: %v", err)
		}

		gpsData := generateGpsDataFromReq(req, integration)
		err = uc.trackingRepo.CreateGpsSensorDataLake(ctx, uc.BQ.Conn(), &gpsData)
		if err != nil {
			commonlogger.Errorf("falied to save gps data to big query err: %v", err)
		}

		doUpsertLatestIntegrationData = true
	}

	if req.IsGeneralDataValid() {
		generalData := generateGeneralDataFromReq(req, integration)
		err := uc.trackingRepo.CreateGeneralSensor(ctx, uc.DBTimeScale.DB(), &generalData)
		if err != nil {
			commonlogger.Errorf("falied to save general data err: %v", err)
		}

		populateReqMapGeneratedDataGeneral(reqMap, generalData)

		err = uc.trackingRepo.CreateGeneralSensorDataLake(ctx, uc.BQ.Conn(), &generalData)
		if err != nil {
			commonlogger.Errorf("falied to save general data to big query err: %v", err)
		}

		doUpsertLatestIntegrationData = true
	}

	if req.IsTyreSensorDataValid() {
		tyreSensorData := uc.generateTyreSensorDataFromReq(req, integration)
		err := uc.trackingRepo.CreateTyreSensor(ctx, uc.DBTimeScale.DB(), &tyreSensorData)
		if err != nil {
			commonlogger.Errorf("falied to save tyre sensor data err: %v", err)
		}

		err = uc.trackingRepo.CreateTyreSensorDataLake(ctx, uc.BQ.Conn(), &tyreSensorData)
		if err != nil {
			commonlogger.Errorf("falied to save tyre sensor data to big query err: %v", err)
		}

		doUpsertLatestIntegrationData = true
	}

	if doUpsertLatestIntegrationData {
		uc.UpsertLatestIntegrationData(ctx, integration, req)
	}

	uc.MonitorAlertV2(context.Background(), integration, reqMap, time.Unix(int64(req.Time), 0).In(time.UTC))
}

func populateReqMapGeneratedDataGeneral(reqMap map[string]interface{}, generalData models.GeneralSensor) {
	if generalData.Pitch.Valid {
		reqMap["general.pitch"] = generalData.Pitch.Float64
	}

	if generalData.Roll.Valid {
		reqMap["general.roll"] = generalData.Roll.Float64
	}
}

func generateCompressorDataFromReq(req dtos.InsertSensorDataReq, integration *integrationModel.Integration) models.CompressorSensorData {
	if req.PressMachineStatus.Valid {
		var machineStatusByte, _ = base64.StdEncoding.DecodeString(req.PressMachineStatus.String)
		req.PressMachineStatus = null.StringFrom(string(machineStatusByte))
	}
	return models.CompressorSensorData{
		AssetID:                    integration.InternalReferenceID,
		Time:                       time.Unix(int64(req.Time), 0).In(time.UTC),
		DeviceID:                   req.Ident,
		CreatedAt:                  time.Now().In(time.UTC),
		IntegrationID:              integration.ID,
		ClientID:                   integration.ClientID,
		PressAirFeedPressure:       req.PressAirFeedPressure,
		PressAirExhaustTemperature: req.PressAirExhaustTemperature,
		PressRunTime:               req.PressRunTime,
		PressLoadTime:              req.PressLoadTime,
		PressPhaseACurrent:         req.PressPhaseACurrent,
		PressPhaseBCurrent:         req.PressPhaseBCurrent,
		PressPhaseCCurrent:         req.PressPhaseCCurrent,
		PressRunState1:             req.PressRunState1,
		PressRunState2:             req.PressRunState2,
		PressOilFilterUsedTime:     req.PressOilFilterUsedTime,
		PressOilSeparatorUsedTime:  req.PressOilSeparatorUsedTime,
		PressAirFilterUsedTime:     req.PressAirFilterUsedTime,
		PressLubeOilUsedTime:       req.PressLubeOilUsedTime,
		PressLubeGreaseUsedTime:    req.PressLubeGreaseUsedTime,
		PressMachineStatus:         req.PressMachineStatus,
		PressCurrentImbalance:      req.PressCurrentImbalance,
	}
}

func (uc *TrackingUseCase) generateCanBusDataFromReq(req dtos.InsertSensorDataReq, reqMap map[string]interface{}, integration *integrationModel.Integration) models.CanBusSensorData {
	item := models.CanBusSensorData{
		AssetID:                               integration.InternalReferenceID,
		Time:                                  time.Unix(int64(req.Time), 0).In(time.UTC),
		Ident:                                 req.Ident,
		CreatedAt:                             time.Now().In(time.UTC),
		IntegrationID:                         integration.ID,
		ClientID:                              integration.ClientID,
		CanAbsFailureIndicatorStatus:          req.CanAbsFailureIndicatorStatus,
		CanAdditionalFrontLightsStatus:        req.CanAdditionalFrontLightsStatus,
		CanAdditionalRearLightsStatus:         req.CanAdditionalRearLightsStatus,
		CanAirConditionStatus:                 req.CanAirConditionStatus,
		CanAirbagIndicatorStatus:              req.CanAirbagIndicatorStatus,
		CanAutomaticRetarderStatus:            req.CanAutomaticRetarderStatus,
		CanBatteryIndicatorStatus:             req.CanBatteryIndicatorStatus,
		CanCarClosedRemoteStatus:              req.CanCarClosedRemoteStatus,
		CanCarClosedStatus:                    req.CanCarClosedStatus,
		CanCentralDifferential4HiStatus:       req.CanCentralDifferential4HiStatus,
		CanCentralDifferential4LoStatus:       req.CanCentralDifferential4LoStatus,
		CanCheckEngineIndicatorStatus:         req.CanCheckEngineIndicatorStatus,
		CanCngStatus:                          req.CanCngStatus,
		CanConnectionState1:                   req.CanConnectionState1,
		CanConnectionState2:                   req.CanConnectionState2,
		CanConnectionState3:                   req.CanConnectionState3,
		CanCoolantLevelLowIndicatorStatus:     req.CanCoolantLevelLowIndicatorStatus,
		CanCruiseStatus:                       req.CanCruiseStatus,
		CanDriveGearStatus:                    req.CanDriveGearStatus,
		CanDriverSeatbeltIndicatorStatus:      req.CanDriverSeatbeltIndicatorStatus,
		CanDriverSeatbeltStatus:               req.CanDriverSeatbeltStatus,
		CanDynamicIgnitionStatus:              req.CanDynamicIgnitionStatus,
		CanElectricEngineStatus:               req.CanElectricEngineStatus,
		CanElectronicPowerControlStatus:       req.CanElectronicPowerControlStatus,
		CanEngineIgnitionStatus:               req.CanEngineIgnitionStatus,
		CanEngineLoadLevel:                    req.CanEngineLoadLevel,
		CanEngineLockStatus:                   req.CanEngineLockStatus,
		CanEngineMotorhours:                   req.CanEngineMotorhours,
		CanEngineRpm:                          req.CanEngineRpm,
		CanEngineTemperature:                  req.CanEngineTemperature,
		CanEngineWorkingStatus:                req.CanEngineWorkingStatus,
		CanEpsIndicatorStatus:                 req.CanEpsIndicatorStatus,
		CanEspIndicatorStatus:                 req.CanEspIndicatorStatus,
		CanEspStatus:                          req.CanEspStatus,
		CanFactoryArmedStatus:                 req.CanFactoryArmedStatus,
		CanFrontDifferentialStatus:            req.CanFrontDifferentialStatus,
		CanFrontFogLightsStatus:               req.CanFrontFogLightsStatus,
		CanFrontLeftDoorStatus:                req.CanFrontLeftDoorStatus,
		CanFrontPassengerSeatbeltStatus:       req.CanFrontPassengerSeatbeltStatus,
		CanFrontPassengerStatus:               req.CanFrontPassengerStatus,
		CanFrontRightDoorStatus:               req.CanFrontRightDoorStatus,
		CanFuelConsumed:                       req.CanFuelConsumed,
		CanFuelLevelLowIndicatorStatus:        req.CanFuelLevelLowIndicatorStatus,
		CanGlowPlugIndicatorStatus:            req.CanGlowPlugIndicatorStatus,
		CanHandbrakeIndicatorStatus:           req.CanHandbrakeIndicatorStatus,
		CanHandbrakeStatus:                    req.CanHandbrakeStatus,
		CanHighBeamStatus:                     req.CanHighBeamStatus,
		CanHoodStatus:                         req.CanHoodStatus,
		CanIgnitionKeyStatus:                  req.CanIgnitionKeyStatus,
		CanInterlockActive:                    req.CanInterlockActive,
		CanLightSignalStatus:                  req.CanLightSignalStatus,
		CanLightsFailureIndicatorStatus:       req.CanLightsFailureIndicatorStatus,
		CanLightsHazardLightsStatus:           req.CanLightsHazardLightsStatus,
		CanLowBeamStatus:                      req.CanLowBeamStatus,
		CanMaintenanceRequiredStatus:          req.CanMaintenanceRequiredStatus,
		CanManualRetarderStatus:               req.CanManualRetarderStatus,
		CanModuleSleepMode:                    req.CanModuleSleepMode,
		CanNeutralGearStatus:                  req.CanNeutralGearStatus,
		CanOilPressureIndicatorStatus:         req.CanOilPressureIndicatorStatus,
		CanOperatorPresentStatus:              req.CanOperatorPresentStatus,
		CanParkingLightsStatus:                req.CanParkingLightsStatus,
		CanParkingStatus:                      req.CanParkingStatus,
		CanPassengerSeatbeltIndicatorStatus:   req.CanPassengerSeatbeltIndicatorStatus,
		CanPedalBrakeStatus:                   req.CanPedalBrakeStatus,
		CanPedalClutchStatus:                  req.CanPedalClutchStatus,
		CanPrivateStatus:                      req.CanPrivateStatus,
		CanProgramID:                          req.CanProgramID,
		CanPtoStatus:                          req.CanPtoStatus,
		CanReadyToDriveIndicatorStatus:        req.CanReadyToDriveIndicatorStatus,
		CanRearCentralPassengerSeatbeltStatus: req.CanRearCentralPassengerSeatbeltStatus,
		CanRearDifferentialStatus:             req.CanRearDifferentialStatus,
		CanRearFogLightsStatus:                req.CanRearFogLightsStatus,
		CanRearLeftDoorStatus:                 req.CanRearLeftDoorStatus,
		CanRearLeftPassengerSeatbeltStatus:    req.CanRearLeftPassengerSeatbeltStatus,
		CanRearRightDoorStatus:                req.CanRearRightDoorStatus,
		CanRearRightPassengerSeatbeltStatus:   req.CanRearRightPassengerSeatbeltStatus,
		CanReverseGearStatus:                  req.CanReverseGearStatus,
		CanRoofOpenedStatus:                   req.CanRoofOpenedStatus,
		CanSootFilterIndicatorStatus:          req.CanSootFilterIndicatorStatus,
		CanStandaloneEngine:                   req.CanStandaloneEngine,
		CanStopIndicatorStatus:                req.CanStopIndicatorStatus,
		CanThrottlePedalLevel:                 req.CanThrottlePedalLevel,
		CanTirePressureLowStatus:              req.CanTirePressureLowStatus,
		CanTrackerCountedFuelConsumed:         req.CanTrackerCountedFuelConsumed,
		CanTrackerCountedMileage:              req.CanTrackerCountedMileage,
		CanTrailerAxleLiftStatus1:             req.CanTrailerAxleLiftStatus1,
		CanTrailerAxleLiftStatus2:             req.CanTrailerAxleLiftStatus2,
		CanTripEngineMotorhours:               req.CanTripEngineMotorhours,
		CanTrunkStatus:                        req.CanTrunkStatus,
		CanVehicleBatteryChargingStatus:       req.CanVehicleBatteryChargingStatus,
		CanVehicleMileage:                     req.CanVehicleMileage,
		CanVehicleSpeed:                       req.CanVehicleSpeed,
		CanWarningIndicatorStatus:             req.CanWarningIndicatorStatus,
		CanWearBrakePadsIndicatorStatus:       req.CanWearBrakePadsIndicatorStatus,
		CanWebastoStatus:                      req.CanWebastoStatus,
		CanFuelLevel:                          req.CanFuelLevel,
		CanEngineOilLevel:                     req.CanEngineOilLevel,

		CanEngineTorque:             req.CanEngineTorque,
		CanAmbientAirTemperature:    req.CanAmbientAirTemperature,
		CanEngineCoolantTemperature: req.CanEngineCoolantTemperature,
		CanFuelConsumption:          req.CanFuelConsumption,
		CanFuelEconomy:              req.CanFuelEconomy,
		CanMilStatus:                req.CanMilStatus,
		CanWheelSpeed:               req.CanWheelSpeed,
		AdblueLevel:                 req.AdblueLevel,

		EngineCoolantTemperature:                               req.EngineCoolantTemperature,
		EngineFuel_1Temperature_1:                              req.EngineFuel_1Temperature_1,
		EngineOilTemperature_1:                                 req.EngineOilTemperature_1,
		EngineTurbocharger_1OilTemperature:                     req.EngineTurbocharger_1OilTemperature,
		EngineIntercoolerTemperature:                           req.EngineIntercoolerTemperature,
		EngineChargeAirCoolerThermostatOpening:                 req.EngineChargeAirCoolerThermostatOpening,
		EngineFuelDeliveryPressure:                             req.EngineFuelDeliveryPressure,
		EngineExtendedCrankcaseBlowByPressure:                  req.EngineExtendedCrankcaseBlowByPressure,
		EngineOilLevel:                                         req.EngineOilLevel,
		EngineOilPressure_1:                                    req.EngineOilPressure_1,
		EngineCrankcasePressure_1:                              req.EngineCrankcasePressure_1,
		EngineCoolantPressure_1:                                req.EngineCoolantPressure_1,
		EngineCoolantLevel_1:                                   req.EngineCoolantLevel_1,
		EngineTotalHoursOfOperation:                            req.EngineTotalHoursOfOperation,
		EngineTotalRevolutions:                                 req.EngineTotalRevolutions,
		FrontAxleSpeed:                                         req.FrontAxleSpeed,
		RelativeSpeedFrontAxleLeftWheel:                        req.RelativeSpeedFrontAxleLeftWheel,
		RelativeSpeedFrontAxleRightWheel:                       req.RelativeSpeedFrontAxleRightWheel,
		RelativeSpeedRearAxle_1LeftWheel:                       req.RelativeSpeedRearAxle_1LeftWheel,
		RelativeSpeedRearAxle_1RightWheel:                      req.RelativeSpeedRearAxle_1RightWheel,
		RelativeSpeedRearAxle_2LeftWheel:                       req.RelativeSpeedRearAxle_2LeftWheel,
		RelativeSpeedRearAxle_2RightWheel:                      req.RelativeSpeedRearAxle_2RightWheel,
		EngineFuelRate:                                         req.EngineFuelRate,
		EngineInstantaneousFuelEconomy:                         req.EngineInstantaneousFuelEconomy,
		EngineAverageFuelEconomy:                               req.EngineAverageFuelEconomy,
		EngineThrottleValve_1Position_1:                        req.EngineThrottleValve_1Position_1,
		EngineThrottleValve_2Position:                          req.EngineThrottleValve_2Position,
		TransmissionDrivelineEngaged:                           req.TransmissionDrivelineEngaged,
		TransmissionTorqueConverterLockupEngaged:               req.TransmissionTorqueConverterLockupEngaged,
		TransmissionShiftInProcess:                             req.TransmissionShiftInProcess,
		TransmissionTorqueConverterLockupTransitionInProcess:   req.TransmissionTorqueConverterLockupTransitionInProcess,
		TransmissionOutputShaftSpeed:                           req.TransmissionOutputShaftSpeed,
		PercentClutchSlip:                                      req.PercentClutchSlip,
		EngineMomentaryOverspeedEnable:                         req.EngineMomentaryOverspeedEnable,
		ProgressiveShiftDisable:                                req.ProgressiveShiftDisable,
		MomentaryEngineMaximumPowerEnable:                      req.MomentaryEngineMaximumPowerEnable,
		TransmissionInputShaftSpeed:                            req.TransmissionInputShaftSpeed,
		SourceAddressOfControllingDeviceForTransmissionControl: req.SourceAddressOfControllingDeviceForTransmissionControl,
		EngineTorqueMode:                                       req.EngineTorqueMode,
		ActualEnginePercentTorqueFractional:                    req.ActualEnginePercentTorqueFractional,
		DriverSDemandEnginePercentTorque:                       req.DriverSDemandEnginePercentTorque,
		ActualEnginePercentTorque:                              req.ActualEnginePercentTorque,
		EngineSpeed:                                            req.EngineSpeed,
		SourceAddressOfControllingDeviceForEngineControl:       req.SourceAddressOfControllingDeviceForEngineControl,
		EngineStarterMode:                                      req.EngineStarterMode,
		EngineDemandPercentTorque:                              req.EngineDemandPercentTorque,
		AcceleratorPedal_1LowIdleSwitch:                        req.AcceleratorPedal_1LowIdleSwitch,
		AcceleratorPedalKickdownSwitch:                         req.AcceleratorPedalKickdownSwitch,
		RoadSpeedLimitStatus:                                   req.RoadSpeedLimitStatus,
		AcceleratorPedal_2LowIdleSwitch:                        req.AcceleratorPedal_2LowIdleSwitch,
		AcceleratorPedal_1Position:                             req.AcceleratorPedal_1Position,
		EnginePercentLoadAtCurrentSpeed:                        req.EnginePercentLoadAtCurrentSpeed,
		RemoteAcceleratorPedalPosition:                         req.RemoteAcceleratorPedalPosition,
		AcceleratorPedal_2Position:                             req.AcceleratorPedal_2Position,
		VehicleAccelerationRateLimitStatus:                     req.VehicleAccelerationRateLimitStatus,
		MomentaryEngineMaximumPowerEnableFeedback:              req.MomentaryEngineMaximumPowerEnableFeedback,
		DpfThermalManagementActive:                             req.DpfThermalManagementActive,
		ScrThermalManagementActive:                             req.ScrThermalManagementActive,
		ActualMaximumAvailableEnginePercentTorque:              req.ActualMaximumAvailableEnginePercentTorque,
		EstimatedPumpingPercentTorque:                          req.EstimatedPumpingPercentTorque,
		TwoSpeedAxleSwitch:                                     req.TwoSpeedAxleSwitch,
		ParkingBrakeSwitch:                                     req.ParkingBrakeSwitch,
		CruiseControlPauseSwitch:                               req.CruiseControlPauseSwitch,
		ParkBrakeReleaseInhibitRequest:                         req.ParkBrakeReleaseInhibitRequest,
		WheelBasedVehicleSpeed:                                 req.WheelBasedVehicleSpeed,
		CruiseControlActive:                                    req.CruiseControlActive,
		CruiseControlEnableSwitch:                              req.CruiseControlEnableSwitch,
		BrakeSwitch:                                            req.BrakeSwitch,
		ClutchSwitch:                                           req.ClutchSwitch,
		CruiseControlSetSwitch:                                 req.CruiseControlSetSwitch,
		CruiseControlCoastDecelerateSwitch:                     req.CruiseControlCoastDecelerateSwitch,
		CruiseControlResumeSwitch:                              req.CruiseControlResumeSwitch,
		CruiseControlAccelerateSwitch:                          req.CruiseControlAccelerateSwitch,
		CruiseControlSetSpeed:                                  req.CruiseControlSetSpeed,
		PtoGovernorState:                                       req.PtoGovernorState,
		CruiseControlStates:                                    req.CruiseControlStates,
		EngineIdleIncrementSwitch:                              req.EngineIdleIncrementSwitch,
		EngineIdleDecrementSwitch:                              req.EngineIdleDecrementSwitch,
		EngineDiagnosticTestModeSwitch:                         req.EngineDiagnosticTestModeSwitch,
		EngineShutdownOverrideSwitch:                           req.EngineShutdownOverrideSwitch,
		EngineExhaustGasRecirculation_1MassFlowRate:            req.EngineExhaustGasRecirculation_1MassFlowRate,
		EngineIntakeAirMassFlowRate:                            req.EngineIntakeAirMassFlowRate,
		EngineExhaustGasRecirculation_2MassFlowRate:            req.EngineExhaustGasRecirculation_2MassFlowRate,
		TargetFreshAirMassFlow:                                 req.TargetFreshAirMassFlow,
		AsrEngineControlActive:                                 req.AsrEngineControlActive,
		AsrBrakeControlActive:                                  req.AsrBrakeControlActive,
		AntiLockBrakingAbsActive:                               req.AntiLockBrakingAbsActive,
		EbsBrakeSwitch:                                         req.EbsBrakeSwitch,
		BrakePedalPosition:                                     req.BrakePedalPosition,
		AbsOffRoadSwitch:                                       req.AbsOffRoadSwitch,
		AsrOffRoadSwitch:                                       req.AsrOffRoadSwitch,
		AsrHillHolderSwitch:                                    req.AsrHillHolderSwitch,
		TractionControlOverrideSwitch:                          req.TractionControlOverrideSwitch,
		AcceleratorInterlockSwitch:                             req.AcceleratorInterlockSwitch,
		EngineDerateSwitch:                                     req.EngineDerateSwitch,
		EngineAuxiliaryShutdownSwitch:                          req.EngineAuxiliaryShutdownSwitch,
		RemoteAcceleratorEnableSwitch:                          req.RemoteAcceleratorEnableSwitch,
		EngineRetarderSelection:                                req.EngineRetarderSelection,
		AbsFullyOperational:                                    req.AbsFullyOperational,
		EbsRedWarningSignal:                                    req.EbsRedWarningSignal,
		AbsEbsAmberWarningSignalPoweredVehicle:                 req.AbsEbsAmberWarningSignalPoweredVehicle,
		AtcAsrInformationSignal:                                req.AtcAsrInformationSignal,
		SourceAddressOfControllingDeviceForBrakeControl:        req.SourceAddressOfControllingDeviceForBrakeControl,
		RailroadModeSwitch:                                     req.RailroadModeSwitch,
		HaltBrakeSwitch:                                        req.HaltBrakeSwitch,
		TrailerAbsStatus:                                       req.TrailerAbsStatus,
		TractorMountedTrailerAbsWarningSignal:                  req.TractorMountedTrailerAbsWarningSignal,
		TransmissionClutch_1Pressure:                           req.TransmissionClutch_1Pressure,
		TransmissionOilLevel_1:                                 req.TransmissionOilLevel_1,
		TransmissionFilterDifferentialPressure:                 req.TransmissionFilterDifferentialPressure,
		Transmission_1OilPressure:                              req.Transmission_1OilPressure,
		Transmission_1OilTemperature_1:                         req.Transmission_1OilTemperature_1,
		TransmissionOilLevel_1HighLow:                          req.TransmissionOilLevel_1HighLow,
		TransmissionOilLevel_1CountdownTimer:                   req.TransmissionOilLevel_1CountdownTimer,
		TransmissionOilLevel_1MeasurementStatus:                req.TransmissionOilLevel_1MeasurementStatus,
		PneumaticSupplyPressure:                                req.PneumaticSupplyPressure,
		ParkingAndOrTrailerAirPressure:                         req.ParkingAndOrTrailerAirPressure,
		ServiceBrakeCircuit_1AirPressure:                       req.ServiceBrakeCircuit_1AirPressure,
		ServiceBrakeCircuit_2AirPressure:                       req.ServiceBrakeCircuit_2AirPressure,
		AuxiliaryEquipmentSupplyPressure:                       req.AuxiliaryEquipmentSupplyPressure,
		AirSuspensionSupplyPressure_1:                          req.AirSuspensionSupplyPressure_1,
		AirCompressorStatus:                                    req.AirCompressorStatus,
		PowertrainCircuitAirSupplyPressure:                     req.PowertrainCircuitAirSupplyPressure,
		EngineExhaust_1NOx_1:                                   req.EngineExhaust_1NOx_1,
		EngineExhaust_1PercentOxygen_1:                         req.EngineExhaust_1PercentOxygen_1,
		EngineExhaust_1GasSensor_1PowerInRange:                 req.EngineExhaust_1GasSensor_1PowerInRange,
		EngineExhaust_1GasSensor_1AtTemperature:                req.EngineExhaust_1GasSensor_1AtTemperature,
		EngineExhaust_1NOx_1ReadingStable:                      req.EngineExhaust_1NOx_1ReadingStable,
		EngineExhaust_1WideRangePercentOxygen_1ReadingStable:   req.EngineExhaust_1WideRangePercentOxygen_1ReadingStable,
		EngineExhaust_1GasSensor_1HeaterPreliminaryFmi:         req.EngineExhaust_1GasSensor_1HeaterPreliminaryFmi,
		EngineExhaust_1GasSensor_1HeaterControl:                req.EngineExhaust_1GasSensor_1HeaterControl,
		EngineExhaust_1NOxSensor_1PreliminaryFmi:               req.EngineExhaust_1NOxSensor_1PreliminaryFmi,
		EngineExhaust_1NOxSensor_1SelfDiagnosisStatus:          req.EngineExhaust_1NOxSensor_1SelfDiagnosisStatus,
		EngineExhaust_1OxygenSensor_1PreliminaryFmi:            req.EngineExhaust_1OxygenSensor_1PreliminaryFmi,
		EngineTripFuelHighResolution:                           req.EngineTripFuelHighResolution,
		EngineTotalFuelUsedHighResolution:                      req.EngineTotalFuelUsedHighResolution,
		Aftertreatment_1ScrIntakeTemperature:                   req.Aftertreatment_1ScrIntakeTemperature,
		Aftertreatment_1ScrIntakeTemperaturePreliminaryFmi:     req.Aftertreatment_1ScrIntakeTemperaturePreliminaryFmi,
		Aftertreatment_1ScrOutletTemperature:                   req.Aftertreatment_1ScrOutletTemperature,
		Aftertreatment_1ScrOutletTemperaturePreliminaryFmi:     req.Aftertreatment_1ScrOutletTemperaturePreliminaryFmi,
		PoweredVehicleWeight:                                   req.PoweredVehicleWeight,
		GrossCombinationVehicleWeight:                          req.GrossCombinationVehicleWeight,
		GrossCombinationVehicleWeightConfidence:                req.GrossCombinationVehicleWeightConfidence,
		TotalVehicleDistanceHighResolution:                     req.TotalVehicleDistanceHighResolution,
		TripDistanceHighResolution:                             req.TripDistanceHighResolution,
		Seconds:                                                req.Seconds,
		Minutes:                                                req.Minutes,
		Hours:                                                  req.Hours,
		Month:                                                  req.Month,
		Day:                                                    req.Day,
		Year:                                                   req.Year,
		LocalMinuteOffset:                                      req.LocalMinuteOffset,
		LocalHourOffset:                                        req.LocalHourOffset,
		BarometricPressure:                                     req.BarometricPressure,
		CabInteriorTemperature:                                 req.CabInteriorTemperature,
		AmbientAirTemperature:                                  req.AmbientAirTemperature,
		EngineIntake_1AirTemperature:                           req.EngineIntake_1AirTemperature,
		RoadSurfaceTemperature:                                 req.RoadSurfaceTemperature,
		Aftertreatment_1DieselParticulateFilterIntakePressure:  req.Aftertreatment_1DieselParticulateFilterIntakePressure,
		EngineIntakeManifold_1Pressure:                         req.EngineIntakeManifold_1Pressure,
		EngineIntakeManifold_1Temperature:                      req.EngineIntakeManifold_1Temperature,
		EngineIntakeAirPressure:                                req.EngineIntakeAirPressure,
		EngineAirFilter_1DifferentialPressure:                  req.EngineAirFilter_1DifferentialPressure,
		EngineExhaustTemperature:                               req.EngineExhaustTemperature,
		EngineCoolantFilterDifferentialPressure:                req.EngineCoolantFilterDifferentialPressure,
	}

	if req.HasCanManualData {
		uc.populateManualCanBusDataFromReq(&item, reqMap, req.CanManualData)
	}

	return item
}

var canIDMapping *integrationModel.CanIDMappingType

func (uc *TrackingUseCase) getCanIDMapping() {
	if canIDMapping != nil {
		return
	}

	canIDMapping = integrationModel.NewCanIDMapping()
	manualCanBusMappings, err := uc.integrationRepo.GetManualCanBusMappings(context.Background(), uc.DB.DB(),
		integrationModel.ManualCanBusMappingCondition{},
	)
	if err != nil {
		commonlogger.Errorf("cant get manual can bus mapping, err %v", err)
		return
	}

	for _, manualCanBusMapping := range manualCanBusMappings {
		canIDMapping.Append(manualCanBusMapping.CanIdCode, manualCanBusMapping)
	}
}

func (uc *TrackingUseCase) getCanIDMappingByKey(key string) {
	manualCanBusMappings, err := uc.integrationRepo.GetManualCanBusMappings(context.Background(), uc.DB.DB(),
		integrationModel.ManualCanBusMappingCondition{
			Where: integrationModel.ManualCanBusMappingWhere{
				CanIdCode: key,
			},
		},
	)
	if err != nil {
		commonlogger.Errorf("cant get manual can bus mapping, err %v", err)
		return
	}

	canIDMapping.Set(key, manualCanBusMappings)
}

func copyNullFloatIfNotValid(target *null.Float, source null.Float) {
	if !target.Valid {
		*target = source
	}
}

func (uc *TrackingUseCase) populateManualCanBusDataFromReq(canBusData *models.CanBusSensorData, reqMap map[string]interface{}, canManualData map[string]string) {
	uc.getCanIDMapping()

	data := map[string]interface{}{}
	for key, val := range canManualData {
		key = key[strings.LastIndex(key, ".")+1:]
		manualCanBusMappings, ok := canIDMapping.Get(key)
		if !ok {
			uc.getCanIDMappingByKey(key)
			manualCanBusMappings, _ = canIDMapping.Get(key)
		}

		for _, mapping := range manualCanBusMappings {
			val, err := mapping.GetValueFromString(val)
			if err != nil {
				commonlogger.Errorf("cant get manual can bus mapping, err %v", err)
				return
			}

			data[mapping.ParameterCode] = val
			reqMap[mapping.ParameterCode] = val
		}
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		commonlogger.Errorf("failed to marshal data, err %v", err)
		return
	}

	var req dtos.CanManualSensorDataReq
	err = json.Unmarshal(jsonData, &req)
	if err != nil {
		commonlogger.Errorf("failed to unmarshal data, err %v", err)
		return
	}

	// Hack PDT and BAJA
	copyNullFloatIfNotValid(&canBusData.CanEngineTemperature, req.CanEngineTemperature)
	copyNullFloatIfNotValid(&canBusData.EngineCoolantTemperature, req.EngineCoolantTemperature)
	copyNullFloatIfNotValid(&canBusData.EngineFuel_1Temperature_1, req.EngineFuel_1Temperature_1)
	copyNullFloatIfNotValid(&canBusData.EngineOilTemperature_1, req.EngineOilTemperature_1)
	copyNullFloatIfNotValid(&canBusData.EngineTurbocharger_1OilTemperature, req.EngineTurbocharger_1OilTemperature)
	copyNullFloatIfNotValid(&canBusData.EngineIntercoolerTemperature, req.EngineIntercoolerTemperature)
	copyNullFloatIfNotValid(&canBusData.EngineChargeAirCoolerThermostatOpening, req.EngineChargeAirCoolerThermostatOpening)
	copyNullFloatIfNotValid(&canBusData.EngineFuelDeliveryPressure, req.EngineFuelDeliveryPressure)
	copyNullFloatIfNotValid(&canBusData.EngineExtendedCrankcaseBlowByPressure, req.EngineExtendedCrankcaseBlowByPressure)
	copyNullFloatIfNotValid(&canBusData.EngineOilLevel, req.EngineOilLevel)
	copyNullFloatIfNotValid(&canBusData.EngineOilPressure_1, req.EngineOilPressure_1)
	copyNullFloatIfNotValid(&canBusData.EngineCrankcasePressure_1, req.EngineCrankcasePressure_1)
	copyNullFloatIfNotValid(&canBusData.EngineCoolantPressure_1, req.EngineCoolantPressure_1)
	copyNullFloatIfNotValid(&canBusData.EngineCoolantLevel_1, req.EngineCoolantLevel_1)
	copyNullFloatIfNotValid(&canBusData.EngineTotalHoursOfOperation, req.EngineTotalHoursOfOperation)
	copyNullFloatIfNotValid(&canBusData.EngineTotalRevolutions, req.EngineTotalRevolutions)
	copyNullFloatIfNotValid(&canBusData.FrontAxleSpeed, req.FrontAxleSpeed)
	copyNullFloatIfNotValid(&canBusData.RelativeSpeedFrontAxleLeftWheel, req.RelativeSpeedFrontAxleLeftWheel)
	copyNullFloatIfNotValid(&canBusData.RelativeSpeedFrontAxleRightWheel, req.RelativeSpeedFrontAxleRightWheel)
	copyNullFloatIfNotValid(&canBusData.RelativeSpeedRearAxle_1LeftWheel, req.RelativeSpeedRearAxle_1LeftWheel)
	copyNullFloatIfNotValid(&canBusData.RelativeSpeedRearAxle_1RightWheel, req.RelativeSpeedRearAxle_1RightWheel)
	copyNullFloatIfNotValid(&canBusData.RelativeSpeedRearAxle_2LeftWheel, req.RelativeSpeedRearAxle_2LeftWheel)
	copyNullFloatIfNotValid(&canBusData.RelativeSpeedRearAxle_2RightWheel, req.RelativeSpeedRearAxle_2RightWheel)
	copyNullFloatIfNotValid(&canBusData.EngineFuelRate, req.EngineFuelRate)
	copyNullFloatIfNotValid(&canBusData.EngineInstantaneousFuelEconomy, req.EngineInstantaneousFuelEconomy)
	copyNullFloatIfNotValid(&canBusData.EngineAverageFuelEconomy, req.EngineAverageFuelEconomy)
	copyNullFloatIfNotValid(&canBusData.EngineThrottleValve_1Position_1, req.EngineThrottleValve_1Position_1)
	copyNullFloatIfNotValid(&canBusData.EngineThrottleValve_2Position, req.EngineThrottleValve_2Position)
	copyNullFloatIfNotValid(&canBusData.TransmissionDrivelineEngaged, req.TransmissionDrivelineEngaged)
	copyNullFloatIfNotValid(&canBusData.TransmissionTorqueConverterLockupEngaged, req.TransmissionTorqueConverterLockupEngaged)
	copyNullFloatIfNotValid(&canBusData.TransmissionShiftInProcess, req.TransmissionShiftInProcess)
	copyNullFloatIfNotValid(&canBusData.TransmissionTorqueConverterLockupTransitionInProcess, req.TransmissionTorqueConverterLockupTransitionInProcess)
	copyNullFloatIfNotValid(&canBusData.TransmissionOutputShaftSpeed, req.TransmissionOutputShaftSpeed)
	copyNullFloatIfNotValid(&canBusData.PercentClutchSlip, req.PercentClutchSlip)
	copyNullFloatIfNotValid(&canBusData.EngineMomentaryOverspeedEnable, req.EngineMomentaryOverspeedEnable)
	copyNullFloatIfNotValid(&canBusData.ProgressiveShiftDisable, req.ProgressiveShiftDisable)
	copyNullFloatIfNotValid(&canBusData.MomentaryEngineMaximumPowerEnable, req.MomentaryEngineMaximumPowerEnable)
	copyNullFloatIfNotValid(&canBusData.TransmissionInputShaftSpeed, req.TransmissionInputShaftSpeed)
	copyNullFloatIfNotValid(&canBusData.SourceAddressOfControllingDeviceForTransmissionControl, req.SourceAddressOfControllingDeviceForTransmissionControl)
	copyNullFloatIfNotValid(&canBusData.EngineTorqueMode, req.EngineTorqueMode)
	copyNullFloatIfNotValid(&canBusData.ActualEnginePercentTorqueFractional, req.ActualEnginePercentTorqueFractional)
	copyNullFloatIfNotValid(&canBusData.DriverSDemandEnginePercentTorque, req.DriverSDemandEnginePercentTorque)
	copyNullFloatIfNotValid(&canBusData.ActualEnginePercentTorque, req.ActualEnginePercentTorque)
	copyNullFloatIfNotValid(&canBusData.EngineSpeed, req.EngineSpeed)
	copyNullFloatIfNotValid(&canBusData.SourceAddressOfControllingDeviceForEngineControl, req.SourceAddressOfControllingDeviceForEngineControl)
	copyNullFloatIfNotValid(&canBusData.EngineStarterMode, req.EngineStarterMode)
	copyNullFloatIfNotValid(&canBusData.EngineDemandPercentTorque, req.EngineDemandPercentTorque)
	copyNullFloatIfNotValid(&canBusData.AcceleratorPedal_1LowIdleSwitch, req.AcceleratorPedal_1LowIdleSwitch)
	copyNullFloatIfNotValid(&canBusData.AcceleratorPedalKickdownSwitch, req.AcceleratorPedalKickdownSwitch)
	copyNullFloatIfNotValid(&canBusData.RoadSpeedLimitStatus, req.RoadSpeedLimitStatus)
	copyNullFloatIfNotValid(&canBusData.AcceleratorPedal_2LowIdleSwitch, req.AcceleratorPedal_2LowIdleSwitch)
	copyNullFloatIfNotValid(&canBusData.AcceleratorPedal_1Position, req.AcceleratorPedal_1Position)
	copyNullFloatIfNotValid(&canBusData.EnginePercentLoadAtCurrentSpeed, req.EnginePercentLoadAtCurrentSpeed)
	copyNullFloatIfNotValid(&canBusData.RemoteAcceleratorPedalPosition, req.RemoteAcceleratorPedalPosition)
	copyNullFloatIfNotValid(&canBusData.AcceleratorPedal_2Position, req.AcceleratorPedal_2Position)
	copyNullFloatIfNotValid(&canBusData.VehicleAccelerationRateLimitStatus, req.VehicleAccelerationRateLimitStatus)
	copyNullFloatIfNotValid(&canBusData.MomentaryEngineMaximumPowerEnableFeedback, req.MomentaryEngineMaximumPowerEnableFeedback)
	copyNullFloatIfNotValid(&canBusData.DpfThermalManagementActive, req.DpfThermalManagementActive)
	copyNullFloatIfNotValid(&canBusData.ScrThermalManagementActive, req.ScrThermalManagementActive)
	copyNullFloatIfNotValid(&canBusData.ActualMaximumAvailableEnginePercentTorque, req.ActualMaximumAvailableEnginePercentTorque)
	copyNullFloatIfNotValid(&canBusData.EstimatedPumpingPercentTorque, req.EstimatedPumpingPercentTorque)
	copyNullFloatIfNotValid(&canBusData.TwoSpeedAxleSwitch, req.TwoSpeedAxleSwitch)
	copyNullFloatIfNotValid(&canBusData.ParkingBrakeSwitch, req.ParkingBrakeSwitch)
	copyNullFloatIfNotValid(&canBusData.CruiseControlPauseSwitch, req.CruiseControlPauseSwitch)
	copyNullFloatIfNotValid(&canBusData.ParkBrakeReleaseInhibitRequest, req.ParkBrakeReleaseInhibitRequest)
	copyNullFloatIfNotValid(&canBusData.WheelBasedVehicleSpeed, req.WheelBasedVehicleSpeed)
	copyNullFloatIfNotValid(&canBusData.CruiseControlActive, req.CruiseControlActive)
	copyNullFloatIfNotValid(&canBusData.CruiseControlEnableSwitch, req.CruiseControlEnableSwitch)
	copyNullFloatIfNotValid(&canBusData.BrakeSwitch, req.BrakeSwitch)
	copyNullFloatIfNotValid(&canBusData.ClutchSwitch, req.ClutchSwitch)
	copyNullFloatIfNotValid(&canBusData.CruiseControlSetSwitch, req.CruiseControlSetSwitch)
	copyNullFloatIfNotValid(&canBusData.CruiseControlCoastDecelerateSwitch, req.CruiseControlCoastDecelerateSwitch)
	copyNullFloatIfNotValid(&canBusData.CruiseControlResumeSwitch, req.CruiseControlResumeSwitch)
	copyNullFloatIfNotValid(&canBusData.CruiseControlAccelerateSwitch, req.CruiseControlAccelerateSwitch)
	copyNullFloatIfNotValid(&canBusData.CruiseControlSetSpeed, req.CruiseControlSetSpeed)
	copyNullFloatIfNotValid(&canBusData.PtoGovernorState, req.PtoGovernorState)
	copyNullFloatIfNotValid(&canBusData.CruiseControlStates, req.CruiseControlStates)
	copyNullFloatIfNotValid(&canBusData.EngineIdleIncrementSwitch, req.EngineIdleIncrementSwitch)
	copyNullFloatIfNotValid(&canBusData.EngineIdleDecrementSwitch, req.EngineIdleDecrementSwitch)
	copyNullFloatIfNotValid(&canBusData.EngineDiagnosticTestModeSwitch, req.EngineDiagnosticTestModeSwitch)
	copyNullFloatIfNotValid(&canBusData.EngineShutdownOverrideSwitch, req.EngineShutdownOverrideSwitch)
	copyNullFloatIfNotValid(&canBusData.EngineExhaustGasRecirculation_1MassFlowRate, req.EngineExhaustGasRecirculation_1MassFlowRate)
	copyNullFloatIfNotValid(&canBusData.EngineIntakeAirMassFlowRate, req.EngineIntakeAirMassFlowRate)
	copyNullFloatIfNotValid(&canBusData.EngineExhaustGasRecirculation_2MassFlowRate, req.EngineExhaustGasRecirculation_2MassFlowRate)
	copyNullFloatIfNotValid(&canBusData.TargetFreshAirMassFlow, req.TargetFreshAirMassFlow)
	copyNullFloatIfNotValid(&canBusData.AsrEngineControlActive, req.AsrEngineControlActive)
	copyNullFloatIfNotValid(&canBusData.AsrBrakeControlActive, req.AsrBrakeControlActive)
	copyNullFloatIfNotValid(&canBusData.AntiLockBrakingAbsActive, req.AntiLockBrakingAbsActive)
	copyNullFloatIfNotValid(&canBusData.EbsBrakeSwitch, req.EbsBrakeSwitch)
	copyNullFloatIfNotValid(&canBusData.BrakePedalPosition, req.BrakePedalPosition)
	copyNullFloatIfNotValid(&canBusData.AbsOffRoadSwitch, req.AbsOffRoadSwitch)
	copyNullFloatIfNotValid(&canBusData.AsrOffRoadSwitch, req.AsrOffRoadSwitch)
	copyNullFloatIfNotValid(&canBusData.AsrHillHolderSwitch, req.AsrHillHolderSwitch)
	copyNullFloatIfNotValid(&canBusData.TractionControlOverrideSwitch, req.TractionControlOverrideSwitch)
	copyNullFloatIfNotValid(&canBusData.AcceleratorInterlockSwitch, req.AcceleratorInterlockSwitch)
	copyNullFloatIfNotValid(&canBusData.EngineDerateSwitch, req.EngineDerateSwitch)
	copyNullFloatIfNotValid(&canBusData.EngineAuxiliaryShutdownSwitch, req.EngineAuxiliaryShutdownSwitch)
	copyNullFloatIfNotValid(&canBusData.RemoteAcceleratorEnableSwitch, req.RemoteAcceleratorEnableSwitch)
	copyNullFloatIfNotValid(&canBusData.EngineRetarderSelection, req.EngineRetarderSelection)
	copyNullFloatIfNotValid(&canBusData.AbsFullyOperational, req.AbsFullyOperational)
	copyNullFloatIfNotValid(&canBusData.EbsRedWarningSignal, req.EbsRedWarningSignal)
	copyNullFloatIfNotValid(&canBusData.AbsEbsAmberWarningSignalPoweredVehicle, req.AbsEbsAmberWarningSignalPoweredVehicle)
	copyNullFloatIfNotValid(&canBusData.AtcAsrInformationSignal, req.AtcAsrInformationSignal)
	copyNullFloatIfNotValid(&canBusData.SourceAddressOfControllingDeviceForBrakeControl, req.SourceAddressOfControllingDeviceForBrakeControl)
	copyNullFloatIfNotValid(&canBusData.RailroadModeSwitch, req.RailroadModeSwitch)
	copyNullFloatIfNotValid(&canBusData.HaltBrakeSwitch, req.HaltBrakeSwitch)
	copyNullFloatIfNotValid(&canBusData.TrailerAbsStatus, req.TrailerAbsStatus)
	copyNullFloatIfNotValid(&canBusData.TractorMountedTrailerAbsWarningSignal, req.TractorMountedTrailerAbsWarningSignal)
	copyNullFloatIfNotValid(&canBusData.TransmissionClutch_1Pressure, req.TransmissionClutch_1Pressure)
	copyNullFloatIfNotValid(&canBusData.TransmissionOilLevel_1, req.TransmissionOilLevel_1)
	copyNullFloatIfNotValid(&canBusData.TransmissionFilterDifferentialPressure, req.TransmissionFilterDifferentialPressure)
	copyNullFloatIfNotValid(&canBusData.Transmission_1OilPressure, req.Transmission_1OilPressure)
	copyNullFloatIfNotValid(&canBusData.Transmission_1OilTemperature_1, req.Transmission_1OilTemperature_1)
	copyNullFloatIfNotValid(&canBusData.TransmissionOilLevel_1HighLow, req.TransmissionOilLevel_1HighLow)
	copyNullFloatIfNotValid(&canBusData.TransmissionOilLevel_1CountdownTimer, req.TransmissionOilLevel_1CountdownTimer)
	copyNullFloatIfNotValid(&canBusData.TransmissionOilLevel_1MeasurementStatus, req.TransmissionOilLevel_1MeasurementStatus)
	copyNullFloatIfNotValid(&canBusData.PneumaticSupplyPressure, req.PneumaticSupplyPressure)
	copyNullFloatIfNotValid(&canBusData.ParkingAndOrTrailerAirPressure, req.ParkingAndOrTrailerAirPressure)
	copyNullFloatIfNotValid(&canBusData.ServiceBrakeCircuit_1AirPressure, req.ServiceBrakeCircuit_1AirPressure)
	copyNullFloatIfNotValid(&canBusData.ServiceBrakeCircuit_2AirPressure, req.ServiceBrakeCircuit_2AirPressure)
	copyNullFloatIfNotValid(&canBusData.AuxiliaryEquipmentSupplyPressure, req.AuxiliaryEquipmentSupplyPressure)
	copyNullFloatIfNotValid(&canBusData.AirSuspensionSupplyPressure_1, req.AirSuspensionSupplyPressure_1)
	copyNullFloatIfNotValid(&canBusData.AirCompressorStatus, req.AirCompressorStatus)
	copyNullFloatIfNotValid(&canBusData.PowertrainCircuitAirSupplyPressure, req.PowertrainCircuitAirSupplyPressure)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1NOx_1, req.EngineExhaust_1NOx_1)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1PercentOxygen_1, req.EngineExhaust_1PercentOxygen_1)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1GasSensor_1PowerInRange, req.EngineExhaust_1GasSensor_1PowerInRange)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1GasSensor_1AtTemperature, req.EngineExhaust_1GasSensor_1AtTemperature)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1NOx_1ReadingStable, req.EngineExhaust_1NOx_1ReadingStable)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1WideRangePercentOxygen_1ReadingStable, req.EngineExhaust_1WideRangePercentOxygen_1ReadingStable)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1GasSensor_1HeaterPreliminaryFmi, req.EngineExhaust_1GasSensor_1HeaterPreliminaryFmi)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1GasSensor_1HeaterControl, req.EngineExhaust_1GasSensor_1HeaterControl)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1NOxSensor_1PreliminaryFmi, req.EngineExhaust_1NOxSensor_1PreliminaryFmi)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1NOxSensor_1SelfDiagnosisStatus, req.EngineExhaust_1NOxSensor_1SelfDiagnosisStatus)
	copyNullFloatIfNotValid(&canBusData.EngineExhaust_1OxygenSensor_1PreliminaryFmi, req.EngineExhaust_1OxygenSensor_1PreliminaryFmi)
	copyNullFloatIfNotValid(&canBusData.EngineTripFuelHighResolution, req.EngineTripFuelHighResolution)
	copyNullFloatIfNotValid(&canBusData.EngineTotalFuelUsedHighResolution, req.EngineTotalFuelUsedHighResolution)
	copyNullFloatIfNotValid(&canBusData.Aftertreatment_1ScrIntakeTemperature, req.Aftertreatment_1ScrIntakeTemperature)
	copyNullFloatIfNotValid(&canBusData.Aftertreatment_1ScrIntakeTemperaturePreliminaryFmi, req.Aftertreatment_1ScrIntakeTemperaturePreliminaryFmi)
	copyNullFloatIfNotValid(&canBusData.Aftertreatment_1ScrOutletTemperature, req.Aftertreatment_1ScrOutletTemperature)
	copyNullFloatIfNotValid(&canBusData.Aftertreatment_1ScrOutletTemperaturePreliminaryFmi, req.Aftertreatment_1ScrOutletTemperaturePreliminaryFmi)
	copyNullFloatIfNotValid(&canBusData.PoweredVehicleWeight, req.PoweredVehicleWeight)
	copyNullFloatIfNotValid(&canBusData.GrossCombinationVehicleWeight, req.GrossCombinationVehicleWeight)
	copyNullFloatIfNotValid(&canBusData.GrossCombinationVehicleWeightConfidence, req.GrossCombinationVehicleWeightConfidence)
	copyNullFloatIfNotValid(&canBusData.TotalVehicleDistanceHighResolution, req.TotalVehicleDistanceHighResolution)
	copyNullFloatIfNotValid(&canBusData.TripDistanceHighResolution, req.TripDistanceHighResolution)
	copyNullFloatIfNotValid(&canBusData.Seconds, req.Seconds)
	copyNullFloatIfNotValid(&canBusData.Minutes, req.Minutes)
	copyNullFloatIfNotValid(&canBusData.Hours, req.Hours)
	copyNullFloatIfNotValid(&canBusData.Month, req.Month)
	copyNullFloatIfNotValid(&canBusData.Day, req.Day)
	copyNullFloatIfNotValid(&canBusData.Year, req.Year)
	copyNullFloatIfNotValid(&canBusData.LocalMinuteOffset, req.LocalMinuteOffset)
	copyNullFloatIfNotValid(&canBusData.LocalHourOffset, req.LocalHourOffset)
	copyNullFloatIfNotValid(&canBusData.BarometricPressure, req.BarometricPressure)
	copyNullFloatIfNotValid(&canBusData.CabInteriorTemperature, req.CabInteriorTemperature)
	copyNullFloatIfNotValid(&canBusData.AmbientAirTemperature, req.AmbientAirTemperature)
	copyNullFloatIfNotValid(&canBusData.EngineIntake_1AirTemperature, req.EngineIntake_1AirTemperature)
	copyNullFloatIfNotValid(&canBusData.RoadSurfaceTemperature, req.RoadSurfaceTemperature)
	copyNullFloatIfNotValid(&canBusData.Aftertreatment_1DieselParticulateFilterIntakePressure, req.Aftertreatment_1DieselParticulateFilterIntakePressure)
	copyNullFloatIfNotValid(&canBusData.EngineIntakeManifold_1Pressure, req.EngineIntakeManifold_1Pressure)
	copyNullFloatIfNotValid(&canBusData.EngineIntakeManifold_1Temperature, req.EngineIntakeManifold_1Temperature)
	copyNullFloatIfNotValid(&canBusData.EngineIntakeAirPressure, req.EngineIntakeAirPressure)
	copyNullFloatIfNotValid(&canBusData.EngineAirFilter_1DifferentialPressure, req.EngineAirFilter_1DifferentialPressure)
	copyNullFloatIfNotValid(&canBusData.EngineExhaustTemperature, req.EngineExhaustTemperature)
	copyNullFloatIfNotValid(&canBusData.EngineCoolantFilterDifferentialPressure, req.EngineCoolantFilterDifferentialPressure)
	// add more data
}

func generateGpsDataFromReq(req dtos.InsertSensorDataReq, integration *integrationModel.Integration) models.GpsSensor {
	return models.GpsSensor{
		AssetId:            integration.InternalReferenceID,
		Time:               time.Unix(int64(req.Time), 0).In(time.UTC),
		Ident:              req.Ident,
		CreatedAt:          time.Now().In(time.UTC),
		ClientID:           integration.ClientID,
		IntegrationID:      integration.ID,
		PositionAltitude:   req.PositionAltitude,
		PositionDirection:  req.PositionDirection,
		PositionHdop:       req.PositionHdop,
		PositionLatitude:   req.PositionLatitude,
		PositionLongitude:  req.PositionLongitude,
		PositionPdop:       req.PositionPdop,
		PositionSatellites: req.PositionSatellites,
		PositionSpeed:      req.PositionSpeed,
		PositionValid:      req.PositionValid,
		VehicleMileage:     req.VehicleMileage,
	}
}

func generateGpsTrackingFromReq(req dtos.InsertSensorDataReq, integration *integrationModel.Integration) models.Tracking {
	return models.Tracking{
		Time:          time.Unix(int64(req.Time), 0).In(time.UTC),
		Long:          req.PositionLongitude,
		Lat:           req.PositionLatitude,
		KM:            null.NewInt(int64(req.VehicleMileage.Float64), req.VehicleMileage.Valid),
		Speed:         req.PositionSpeed,
		Angle:         null.Int{},
		IMEI:          req.Ident,
		AssetID:       integration.InternalReferenceID,
		TargetCode:    integrationConstants.MapIntegrationTrackingTypeCode()[integration.IntegrationTargetTypeCode],
		CreatedAt:     time.Now().In(time.UTC),
		ClientID:      integration.ClientID,
		IntegrationID: integration.ID,
	}
}

func generateGeneralDataFromReq(req dtos.InsertSensorDataReq, integration *integrationModel.Integration) models.GeneralSensor {
	return models.GeneralSensor{
		AssetID:                    integration.InternalReferenceID,
		Time:                       time.Unix(int64(req.Time), 0).In(time.UTC),
		Ident:                      req.Ident,
		CreatedAt:                  time.Now().In(time.UTC),
		IntegrationID:              integration.ID,
		ClientID:                   integration.ClientID,
		BatteryCurrent:             req.BatteryCurrent,
		BatteryVoltage:             req.BatteryVoltage,
		GsmSignalLevel:             req.GsmSignalLevel,
		GsmOperatorCode:            req.GsmOperatorCode,
		MovementStatus:             req.MovementStatus,
		ExternalPowersourceVoltage: req.ExternalPowersourceVoltage,
		XAccelaration:              req.XAccelaration,
		YAccelaration:              req.YAccelaration,
		ZAccelaration:              req.ZAccelaration,
		PtoDriveEngagementEnum:     req.PtoDriveEngagementEnum,
		FuelConsumed:               req.FuelConsumed,
		SdStatus:                   req.SdStatus,
		DigitalInput:               req.DigitalInput,
		DigitalOutput:              req.DigitalOutput,
		UnplugDetection:            req.UnplugDetection,
	}
}

func (uc *TrackingUseCase) generateTyreSensorDataFromReq(req dtos.InsertSensorDataReq, integration *integrationModel.Integration) models.TyreSensor {
	if !req.TyreIdentMacAddress.Valid {
		var identifier struct {
			MacAdress null.String `json:"mac_address"`
		}
		err := json.Unmarshal(integration.IdentifierJSON.Bytes, &identifier)
		if err == nil {
			req.TyreIdentMacAddress = identifier.MacAdress
		}
	}

	var assetVehicle assetModel.AssetVehicle
	var axleConfigs []assetModel.AxleConfiguration
	if !req.TyrePosition.Valid || !req.TyreParentAssetID.Valid {
		assetLinked, err := uc.assetLinkedRepo.GetAssetLinked(context.Background(), uc.DB.DB(), assetModel.AssetLinkedCondition{
			Where: assetModel.AssetLinkedWhere{
				ChildAssetID: integration.InternalReferenceID,
				WithUnlinked: false,
				TypeCode:     assetConstants.ASSET_LINKED_TYPE_VEHICLE_TYRE,
			},
			Preload: assetModel.AssetLinkedPreload{
				AssetLinkedAssetVehicleTyre: true,
				ParentAsset:                 true,
			},
		})
		if err == nil {
			req.TyrePosition = null.IntFrom(int64(assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition))
			req.TyreParentAssetID = null.StringFrom(assetLinked.ParentAssetID)
			assetVehicle = assetLinked.ParentAsset
			err = assetLinked.ParentAsset.AxleConfiguration.AssignTo(&axleConfigs)
			if err == nil {
				tyrePosToRowPos := assetModel.AxleConfigurationsTyrePositionToRowNumber(axleConfigs)
				if val, ok := tyrePosToRowPos[assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition]; ok {
					req.TyreRowPosition = null.IntFrom(int64(val))
				}
			}
		} else {
			commonlogger.Warnf("error get linked asset tyre %v", err)
		}
	}

	tyreSensor := models.TyreSensor{
		AssetID:         integration.InternalReferenceID,
		Time:            time.Unix(int64(req.Time), 0).In(time.UTC),
		Ident:           req.Ident,
		CreatedAt:       time.Now().In(time.UTC),
		IntegrationID:   integration.ID,
		ClientID:        integration.ClientID,
		IdentMacAddress: req.TyreIdentMacAddress,
		BatteryVoltage:  req.TyreBatteryVoltage,
		BatteryPercent:  req.TyreBatteryPercent,
		Pressure:        req.TyrePressure,
		Temperature:     req.TyreTemperature,
		TyrePosition:    req.TyrePosition,
		ParentAssetID:   req.TyreParentAssetID,
		TyreRowPosition: req.TyreRowPosition,
	}

	// Chck for New Tyre Alert
	if req.TyreParentAssetID.Valid {
		uc.MonitorTyreAlertV2(context.Background(), &tyreSensor, &assetVehicle, axleConfigs)
	}

	return tyreSensor
}
