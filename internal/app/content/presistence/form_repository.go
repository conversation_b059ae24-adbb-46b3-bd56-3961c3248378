package presistence

import (
	"assetfindr/internal/app/content/models"
	"assetfindr/internal/app/content/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type formRepository struct{}

func NewFormRepository() repository.FormRepository {
	return &formRepository{}
}

func (r *formRepository) CreateForm(ctx context.Context, dB database.DBI, form *models.Form) error {
	return dB.GetTx().Create(form).Error
}

func (r *formRepository) CreateFormFields(ctx context.Context, dB database.DBI, formFeilds []models.FormField) error {
	return dB.GetTx().Create(&formFeilds).Error
}

func (r *formRepository) CreateFormTemplateFields(ctx context.Context, dB database.DBI, formTemplateFeilds []models.FormTemplateField) error {
	if len(formTemplateFeilds) == 0 {
		return nil
	}

	return dB.GetTx().Create(&formTemplateFeilds).Error
}

func (r *formRepository) UpsertForm(ctx context.Context, dB database.DBI, form *models.Form) error {
	err := dB.GetTx().
		Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "reference_id"},
				{Name: "form_category_code"},
			},
			TargetWhere: clause.Where{Exprs: []clause.Expression{clause.Eq{Column: "ctn_forms.deleted_at", Value: nil}}},
			DoUpdates:   clause.AssignmentColumns([]string{"updated_at", "status_code"}),
		}).
		Clauses(clause.Returning{Columns: []clause.Column{{Name: "id"}}}).
		Create(form).Error
	if err != nil {
		return err
	}

	return nil
}

func (r *formRepository) CreateFormTemplate(ctx context.Context, dB database.DBI, template *models.FormTemplate) error {
	return dB.GetTx().Create(template).Error
}

func (r *formRepository) UpdateFormTemplate(ctx context.Context, dB database.DBI, id string, template *models.FormTemplate) error {
	return dB.GetTx().
		Model(&models.FormTemplate{}).
		Where("id = ?", id).
		Updates(template).
		Error
}

func (r *formRepository) DisableOtherFormTemplatesDefault(ctx context.Context, dB database.DBI, template *models.FormTemplate) error {
	return dB.GetTx().
		Model(&models.FormTemplate{}).
		Where("id != ?", template.ID).
		Where("client_id = ?", template.ClientID).
		Where("form_category_code = ?", template.FormCategoryCode).
		Where("is_default = true").
		Updates(map[string]interface{}{
			"is_default": false,
		}).
		Error
}

func (r *formRepository) DeleteFormFieldByIDs(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.FormField{}, ids).Error
}

func (r *formRepository) DeleteFormByIDs(ctx context.Context, dB database.DBI, ids ...string) error {
	if len(ids) == 0 {
		return nil
	}
	return dB.GetTx().Delete(&models.Form{}, "id IN ?", ids).Error
}

func (r *formRepository) DeleteFormFeildsByFormIDs(ctx context.Context, dB database.DBI, formIDs ...string) error {
	if len(formIDs) == 0 {
		return nil
	}
	return dB.GetTx().Delete(&models.FormField{}, "form_id IN ?", formIDs).Error
}

func (r *formRepository) DeleteFormTemplateFieldByIDs(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.FormTemplateField{}, ids).Error
}

func (r *formRepository) UpdateFormField(ctx context.Context, dB database.DBI, id string, field *models.FormField) error {
	return dB.GetTx().
		Model(&models.FormField{}).
		Where("id = ?", id).
		Updates(field).
		Error
}

func (r *formRepository) UpdateFormTemplateField(ctx context.Context, dB database.DBI, id string, field *models.FormTemplateField) error {
	return dB.GetTx().
		Model(&models.FormTemplateField{}).
		Where("id = ?", id).
		Updates(field).
		Error
}

func entichFormQueryWithWhere(query *gorm.DB, where models.FormWhere) {
	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.FormCategoryCode != "" {
		query.Where("form_category_code = ?", where.FormCategoryCode)
	} // FormCategoryCode

	if where.ReferenceID != "" {
		query.Where("reference_id = ?", where.ReferenceID)
	} // ReferenceID

	if len(where.ReferenceIDs) > 0 {
		query.Where("reference_id IN ?", where.ReferenceIDs)
	} // ReferenceIDs

	if where.TemplateID != "" {
		query.Where("template_id = ?", where.TemplateID)
	} // TemplateID
}

func entichFormQueryWithPreload(query *gorm.DB, preload models.FormPreload) {
	if preload.FormFields {
		query.Preload("FormFields", func(db *gorm.DB) *gorm.DB {
			return db.Order("sequence ASC")
		})
	}
}

func (r *formRepository) GetForm(ctx context.Context, dB database.DBI, cond models.FormCondition) (*models.Form, error) {
	form := &models.Form{}
	query := dB.GetTx().Model(form)
	entichFormQueryWithWhere(query, cond.Where)
	entichFormQueryWithPreload(query, cond.Preload)

	err := query.First(form).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("form")
		}

		return nil, err
	}

	return form, nil
}
func (r *formRepository) GetForms(ctx context.Context, dB database.DBI, cond models.FormCondition) ([]models.Form, error) {
	forms := []models.Form{}
	query := dB.GetTx().Model(forms)
	entichFormQueryWithWhere(query, cond.Where)
	entichFormQueryWithPreload(query, cond.Preload)

	err := query.Find(&forms).Error
	if err != nil {
		return nil, err
	}

	return forms, nil
}

func entichFormTemplateQueryWithWhere(query *gorm.DB, where models.FormTemplateWhere) {
	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.FormCategoryCode != "" {
		query.Where("form_category_code = ?", where.FormCategoryCode)
	} // FormCategoryCode

	if where.WithOrmDeleted {
		query.Unscoped()
	}
}

func entichFormTemplateQueryWithPreload(query *gorm.DB, preload models.FormTemplatePreload) {
	if preload.FormFields {
		query.Preload("FormFields", func(db *gorm.DB) *gorm.DB {
			return db.Order("sequence ASC")
		})
	}
}

func (r *formRepository) GetFormTemplate(ctx context.Context, dB database.DBI, cond models.FormTemplateCondition) (*models.FormTemplate, error) {
	tmpl := &models.FormTemplate{}
	query := dB.GetTx().Model(tmpl)
	entichFormTemplateQueryWithWhere(query, cond.Where)
	entichFormTemplateQueryWithPreload(query, cond.Preload)

	err := query.First(tmpl).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("form template")
		}

		return nil, err
	}

	return tmpl, nil
}

func (r *formRepository) GetFormTemplateList(ctx context.Context, dB database.DBI, param models.GetFormTemplateListParam) (int, []models.FormTemplate, error) {
	var totalRecords int64
	tmpl := []models.FormTemplate{}
	query := dB.GetTx().Model(&models.FormTemplate{})
	entichFormTemplateQueryWithWhere(query, param.Cond.Where)
	if param.SearchKeyword != "" {
		query.Where("LOWER(name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}
	entichFormTemplateQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("is_default DESC,name ASC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&tmpl).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tmpl, nil
}

// enrichFlowQueryWithWhere applies conditions to the query
func enrichFlowQueryWithWhere(query *gorm.DB, where models.FlowWhere) {
	if where.ID != "" {
		query.Where("ctn_flows.id = ?", where.ID)
	}

	if len(where.IDs) > 0 {
		query.Where("ctn_flows.id IN ?", where.IDs)
	}

	if where.ReferenceID != "" {
		query.Where("ctn_flows.reference_id = ?", where.ReferenceID)
	}
	if where.ReferenceCode != "" {
		query.Where("ctn_flows.reference_code = ?", where.ReferenceCode)
	}
	if where.ClientID != "" {
		query.Where("ctn_flows.client_id = ?", where.ClientID)
	}

}

func enrichFlowQueryWithPreload(query *gorm.DB, preload models.FlowPreload) {
	if preload.FormTemplate {
		query.Preload("FormTemplate.FormFields", func(db *gorm.DB) *gorm.DB {
			return db.Order("sequence ASC")
		})
	}

	if preload.Form {
		query.Preload("Form.FormFields")
	}

}

// GetFlowList retrieves a list of flows based on conditions
func (r *formRepository) GetFlows(ctx context.Context, dB database.DBI, conditions models.FlowCondition) ([]models.Flow, error) {
	var flows []models.Flow
	query := dB.GetTx().Model(&models.Flow{})
	enrichFlowQueryWithWhere(query, conditions.Where)
	enrichFlowQueryWithPreload(query, conditions.Preload)
	query.Order("ctn_flows.created_at ASC")
	err := query.Find(&flows).Error
	if err != nil {
		return nil, err
	}
	return flows, nil
}

func (r *formRepository) GetFlow(ctx context.Context, dB database.DBI, conditions models.FlowCondition) (*models.Flow, error) {
	var flow models.Flow
	query := dB.GetTx().Model(&models.Flow{})
	enrichFlowQueryWithWhere(query, conditions.Where)
	enrichFlowQueryWithPreload(query, conditions.Preload)
	err := query.First(&flow).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("flow")
		}
		return nil, err
	}
	return &flow, nil
}

// CreateFlows inserts new flow records into the database
func (r *formRepository) CreateFlows(ctx context.Context, dB database.DBI, flows []models.Flow) error {
	if len(flows) == 0 {
		return nil
	}
	return dB.GetTx().Create(&flows).Error
}

// DeleteFlows deletes flow records by IDs
func (r *formRepository) DeleteFlows(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}
	return dB.GetTx().Delete(&models.Flow{}, "id IN ?", ids).Error
}

// UpdateFlow updates an existing flow record
func (r *formRepository) UpdateFlow(ctx context.Context, dB database.DBI, id string, flow *models.Flow) error {
	return dB.GetTx().Where("id = ?", id).Updates(flow).Error
}
