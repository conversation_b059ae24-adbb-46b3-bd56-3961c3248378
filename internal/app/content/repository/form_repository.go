package repository

import (
	"assetfindr/internal/app/content/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type FormRepository interface {
	CreateForm(ctx context.Context, dB database.DBI, form *models.Form) error
	UpsertForm(ctx context.Context, dB database.DBI, form *models.Form) error
	CreateFormTemplate(ctx context.Context, dB database.DBI, template *models.FormTemplate) error
	UpdateFormTemplate(ctx context.Context, dB database.DBI, id string, template *models.FormTemplate) error
	DisableOtherFormTemplatesDefault(ctx context.Context, dB database.DBI, template *models.FormTemplate) error
	GetForm(ctx context.Context, dB database.DBI, cond models.FormCondition) (*models.Form, error)
	GetForms(ctx context.Context, dB database.DBI, cond models.FormCondition) ([]models.Form, error)
	GetFormTemplate(ctx context.Context, dB database.DBI, cond models.FormTemplateCondition) (*models.FormTemplate, error)
	GetFormTemplateList(ctx context.Context, dB database.DBI, param models.GetFormTemplateListParam) (int, []models.FormTemplate, error)
	UpdateFormField(ctx context.Context, dB database.DBI, id string, field *models.FormField) error
	UpdateFormTemplateField(ctx context.Context, dB database.DBI, id string, field *models.FormTemplateField) error
	DeleteFormFieldByIDs(ctx context.Context, dB database.DBI, ids []string) error
	DeleteFormByIDs(ctx context.Context, dB database.DBI, ids ...string) error
	DeleteFormFeildsByFormIDs(ctx context.Context, dB database.DBI, formIDs ...string) error
	DeleteFormTemplateFieldByIDs(ctx context.Context, dB database.DBI, ids []string) error
	CreateFormFields(ctx context.Context, dB database.DBI, formFeilds []models.FormField) error
	CreateFormTemplateFields(ctx context.Context, dB database.DBI, formTemplateFeilds []models.FormTemplateField) error

	GetFlows(ctx context.Context, dB database.DBI, conditions models.FlowCondition) ([]models.Flow, error)
	GetFlow(ctx context.Context, dB database.DBI, conditions models.FlowCondition) (*models.Flow, error)
	CreateFlows(ctx context.Context, dB database.DBI, flows []models.Flow) error
	DeleteFlows(ctx context.Context, dB database.DBI, ids []string) error
	UpdateFlow(ctx context.Context, dB database.DBI, id string, flow *models.Flow) error
}
