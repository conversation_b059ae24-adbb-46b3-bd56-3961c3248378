package routers

import (
	"assetfindr/internal/app/user-identity/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterUserIdentityRoutes(route *gin.Engine, userHandler *handler.UserHandler) *gin.Engine {
	// Register
	route.POST("/v1/register", userHandler.UserRegister)

	// Login
	route.POST("/v1/login", userHandler.UserLoginWithFirebaseToken)

	// Logout
	route.POST("/v1/logout", middleware.TokenValidationMiddleware(), userHandler.Logout)

	// Logout
	route.GET("/v1/check-client-alias-availability", userHandler.CheckClientAliasAvailibility)

	// Note: got error CORS if name /v1/users. need to find out why
	userRoutes := route.Group("/v1/user", middleware.TokenValidationMiddleware())
	{
		userRoutes.GET("", userHandler.GetUserList)
		userRoutes.GET("/:id", userHandler.GetUser)
		userRoutes.PUT("/:id", userHandler.UpdateUser)
		userRoutes.PUT("/email", userHandler.UpdateUserEmail)
		userRoutes.DELETE("/:id", userHandler.DeleteUser)
		userRoutes.POST("", userHandler.CreateUser)

		userRoutes.POST("/devices/register", userHandler.CreateUserDevice)
		userRoutes.PUT("/password", userHandler.ChangeUserPassword)
	}

	// Check auth token validation
	route.GET("/v1/auth", middleware.TokenValidationMiddleware(), userHandler.Auth)

	adminUserRoutes := route.Group("/v1/admin/users", middleware.APITokenMiddleware)
	{
		adminUserRoutes.GET("/clients/:client_id", userHandler.GetUserListFromAdmin)
		adminUserRoutes.POST("", userHandler.AddUsersToSubClient)
	}

	registerAFChallengeRoutes := route.Group("/v1/register-af-challenge")
	{
		registerAFChallengeRoutes.POST("", userHandler.RegisterAFChallenge)
	}

	publicUserRoutes := route.Group("/v1/public/users", middleware.APITokenMiddleware)
	{ // Start public user routes
		publicUserRoutes.GET("/clients/:client_id", userHandler.GetUserListFromAdmin)
	} // End public user routes

	return route
}
