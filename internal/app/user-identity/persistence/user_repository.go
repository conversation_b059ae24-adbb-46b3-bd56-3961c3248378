package persistence

import (
	"assetfindr/internal/app/user-identity/constants"
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"fmt"

	"gorm.io/gorm"
)

type UserRepository struct{}

func NewUserRepository() repository.UserRepository {
	return &UserRepository{}
}

func (r *UserRepository) CreateUser(ctx context.Context, dB database.DBI, user *models.User) error {
	return dB.GetTx().Create(user).Error
}

func (r *UserRepository) GetUserByField(ctx context.Context, dB database.DBI, user *models.User, columnName string, columnValue string) error {
	query := fmt.Sprintf("%s = ?", columnName)
	if err := dB.GetOrm().Preload("UserClients").Where(query, columnValue).First(user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}
		return err
	}
	return nil
}

func (r *UserRepository) GetUserByClientAliasAndFirebaseId(ctx context.Context, dB database.DBI, user *models.User, clientAlias string, firebaseToken string) error {
	userclinet := models.UserClient{}
	if err := dB.GetOrm().
		Preload("User").
		Joins("INNER JOIN uis_clients ON uis_clients.id = uis_user_clients.client_id").
		Joins("INNER JOIN uis_users ON uis_users.id = uis_user_clients.user_id").
		Where("uis_clients.client_alias = ?", clientAlias).
		Where("uis_users.firebase_user_id = ?", firebaseToken).
		First(&userclinet).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}
		return err
	}
	*user = *userclinet.User
	return nil
}

func (r *UserRepository) GetUserById(ctx context.Context, dB database.DBI, user *models.User, userId string) error {
	if err := dB.GetOrm().Preload("UserRole").Where("ID = ?", userId).First(user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errorhandler.ErrDataNotFound("USER")
		}
		return err
	}
	return nil
}

func (r *UserRepository) GetUsers(ctx context.Context, dB database.DBI, users *[]models.User) error {
	clientId, err := authhelpers.GetClaimClientIdFromCtx(ctx)
	if err != nil {
		return err
	}

	if err := dB.GetOrm().
		Preload("UserRole").
		Joins("LEFT JOIN uis_user_clients ON uis_users.id = uis_user_clients.user_id").
		Where("uis_user_clients.client_id = ?", clientId).
		Find(users).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}
		return err
	}
	return nil
}

func (r *UserRepository) UpdateUser(ctx context.Context, dB database.DBI, user *models.User) error {
	return dB.GetTx().
		Model(&models.User{}).
		Where("id = ?", user.ID).
		Updates(user).Error
}

func (r *UserRepository) GetUserList(ctx context.Context, dB database.DBI, param models.GetUserListParam) (int, []models.User, error) {
	var totalRecords int64
	users := []models.User{}
	query := dB.GetOrm().Model(&users)

	query.Joins("LEFT JOIN uis_user_clients ON uis_users.id = uis_user_clients.user_id")

	enrichUserQueryWithWhere(query, param.Cond.Where)
	enrichUserQueryWithPreload(query, param.Cond)

	if param.SearchKeyword != "" {
		query.Where("LOWER(CONCAT(uis_users.first_name, ' ', uis_users.last_name)) LIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), users, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&users).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), users, nil
}

func (r *UserRepository) GetUsersByIds(ctx context.Context, dB database.DBI, users *[]models.User, userIds []string) error {
	if err := dB.GetOrm().Where("ID IN ?", userIds).Find(users).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}
		return err
	}
	return nil
}

func (r *UserRepository) GetUsersInMapByIds(ctx context.Context, dB database.DBI, usersMapById *map[string]models.User, userIds []string) error {
	var users []models.User

	if err := dB.GetOrm().Where("id IN (?)", userIds).Find(&users).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return err
	}

	// Ensure 'usersMapById' is initialized before storing users
	if *usersMapById == nil {
		*usersMapById = make(map[string]models.User)
	}

	for _, user := range users {
		(*usersMapById)[user.ID] = user
	}

	return nil
}

func enrichUserQueryWithWhere(query *gorm.DB, where models.UserWhere) {
	if where.WithOrmDeleted {
		query.Unscoped()
	}
	if where.ID != "" {
		query.Where("uis_users.id = ?", where.ID)
	}

	if where.PermissionGroupID != "" {
		query.Where("uis_user_clients.permission_group_id = ?", where.PermissionGroupID)
	}

	if len(where.IDs) > 0 {
		query.Where("uis_users.id IN ?", where.IDs)
	}

	if where.Email != "" {
		query.Where("uis_users.email = ?", where.Email)
	}

	if len(where.Emails) > 0 {
		query.Where("uis_users.email IN ?", where.Emails)
	}

	if where.ClientID != "" {
		query.Where(
			"uis_user_clients.client_id = ?",
			where.ClientID,
		)
	}

	if !where.IsShowAll {
		query.Where("uis_users.status_code = ?", constants.STATUS_ACTIVE)
	}

	if len(where.DepartmentIDs) > 0 {
		query.Where("uis_user_clients.department_id IN ?", where.DepartmentIDs)
	}

	if len(where.WialonAccountIds) > 0 {
		query.Where("uis_user_clients.wialon_account_id IN ?", where.WialonAccountIds).
			Where("uis_user_clients.is_block_wialon_account is false")
	}

	if where.HasActiveWialonAccount {
		query.Where("uis_user_clients.wialon_account_id != ''").
			Where("uis_user_clients.wialon_account_id IS NOT NULL").
			Where("uis_user_clients.is_block_wialon_account is false")
	}

}

func enrichUserQueryWithPreload(query *gorm.DB, condition models.UserCondition) {
	if condition.Preload.ActiveDevices {
		query.Preload("Devices", func(db *gorm.DB) *gorm.DB {
			daysActiveLimit := "3"
			return db.
				Where("uis_user_devices.last_activity_date > NOW() - (? || ' days')::interval", daysActiveLimit).
				Where("uis_user_devices.status_code = ?", constants.DEVICE_STATUS_CODE_ACTIVE)
		})
	}

	if condition.Preload.CurrentUserClientGroup {
		query.Preload("CurrentUserClient", func(db *gorm.DB) *gorm.DB {
			return db.Where("uis_user_clients.client_id = ?", condition.Where.ClientID)
		}).Preload("CurrentUserClient.Group").Preload("CurrentUserClient.Department")
	}

	if condition.Preload.CurrentUserClientDepartment {
		query.Preload("CurrentUserClient", func(db *gorm.DB) *gorm.DB {
			return db.Where("uis_user_clients.client_id = ?", condition.Where.ClientID)
		}).Preload("CurrentUserClient.Department")
	}
}

func (r *UserRepository) GetUser(ctx context.Context, dB database.DBI, condition models.UserCondition) (*models.User, error) {
	user := models.User{}
	query := dB.GetOrm().Model(&user)

	query.Joins("LEFT JOIN uis_user_clients ON uis_users.id = uis_user_clients.user_id")

	enrichUserQueryWithWhere(query, condition.Where)

	enrichUserQueryWithPreload(query, condition)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("user")
		}

		return nil, err
	}

	return &user, nil
}

func (r *UserRepository) GetUsersV2(ctx context.Context, dB database.DBI, condition models.UserCondition) ([]models.User, error) {
	users := []models.User{}
	query := dB.GetOrm().Model(&users)

	query.Joins("LEFT JOIN uis_user_clients ON uis_users.id = uis_user_clients.user_id")

	enrichUserQueryWithWhere(query, condition.Where)

	enrichUserQueryWithPreload(query, condition)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	err := query.Find(&users).Error
	if err != nil {
		return nil, err
	}

	return users, nil
}

func (r *UserRepository) GetUserFirebaseDeviceTokens(ctx context.Context, dB database.DBI, userID string) ([]string, error) {
	userTokens := []string{}
	daysActiveLimit := "3"
	err := dB.GetOrm().
		Model(&models.UserDevice{}).
		Where("uis_user_devices.last_activity_date > NOW() - (? || ' days')::interval", daysActiveLimit).
		Where("uis_user_devices.status_code = ?", constants.DEVICE_STATUS_CODE_ACTIVE).
		Where("uis_user_devices.user_id = ?", userID).
		Pluck("uis_user_devices.firebase_device_token", &userTokens).Error
	if err != nil {
		return nil, err
	}

	return userTokens, nil
}

func (r *UserRepository) GetClientByAlias(ctx context.Context, dB database.DBI, clientAlias string) (models.Client, error) {
	client := models.Client{}
	if err := dB.GetOrm().
		Where("client_alias = ?", clientAlias).
		First(&client).Error; err != nil {
		return client, err
	}
	return client, nil
}
