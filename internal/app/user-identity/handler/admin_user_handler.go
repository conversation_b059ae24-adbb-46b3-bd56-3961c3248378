package handler

import (
	"assetfindr/internal/app/user-identity/dtos"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *UserHandler) GetUserListFromAdmin(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetUserListFromAdminReq{}
	clientID := c.Param("client_id")
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.UserUseCase.GetUserListFromAdmin(ctx, clientID, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.<PERSON>(http.StatusOK, resp)
}

func (h *UserHandler) AddUsersToSubClient(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.AddUsersToSubClientReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.UserUseCase.AddUsersToSubClient(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}
