package taskservice

import (
	approvalPersistence "assetfindr/internal/app/approval/persistence"
	approvalUsecase "assetfindr/internal/app/approval/usecase"
	assetServicePersistence "assetfindr/internal/app/asset/persistence"
	assetServiceUsecase "assetfindr/internal/app/asset/usecase"
	assetUtils "assetfindr/internal/app/asset/utils"
	contentPersistence "assetfindr/internal/app/content/presistence"
	contentUsecase "assetfindr/internal/app/content/usecase"
	financePresistence "assetfindr/internal/app/finance/presistence"
	financeUseCase "assetfindr/internal/app/finance/usecase"
	integrationPersistence "assetfindr/internal/app/integration/presistence"
	"assetfindr/internal/app/inventory/persistence"
	packagePersistence "assetfindr/internal/app/inventory/persistence"
	notificationPersistence "assetfindr/internal/app/notification/presistence"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storagePersistence "assetfindr/internal/app/storage/persistence"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	taskHandlr "assetfindr/internal/app/task/handler"
	taskPersistence "assetfindr/internal/app/task/persistence"
	taskRouters "assetfindr/internal/app/task/routers"
	taskUsecase "assetfindr/internal/app/task/usecase"
	userIdentityPersistence "assetfindr/internal/app/user-identity/persistence"

	"assetfindr/internal/infrastructure/cloudStorage"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/email"

	"github.com/gin-gonic/gin"
)

func InitializeTicket(dbUsecase database.DBUsecase, router *gin.Engine) error {

	// Repostiories
	userIdentityRepository := userIdentityPersistence.NewUserRepository()
	ticketRepository := taskPersistence.NewTicketRepository()
	storageRepository := storagePersistence.NewStorageRepository(cloudStorage.Bucket)
	attachmentRepository := storagePersistence.NewAttachmentRepository()
	assetRepository := assetServicePersistence.NewAssetRepository()
	assetAssignmentRepository := assetServicePersistence.NewAssetAssignmentRepository()
	assetLinkedRepository := assetServicePersistence.NewAssetLinkedRepository()
	assetLogRepository := assetServicePersistence.NewAssetLogRepository()
	assetVehicleRepository := assetServicePersistence.NewAssetVehicleRepository()
	assetTyreRepository := assetServicePersistence.NewAssetTyreRepository()
	notificationRepo := notificationPersistence.NewAssetTyreRepository()
	emailRepo := notificationPersistence.NewEmailRepository(email.GetEmailService())
	financeRepo := financePresistence.NewFinanceRepository()
	formRepository := contentPersistence.NewFormRepository()
	departmentRepository := userIdentityPersistence.NewDepartmentRepository()
	assetComponentRepo := assetServicePersistence.NewAssetComponentRepository()
	assetInspectionRepo := assetServicePersistence.NewAssetInspectionRepository()
	assetInspectionTyreRepository := assetServicePersistence.NewAssetInspectionTyreRepository()
	assetInspectionVehicleRepository := assetServicePersistence.NewAssetInspectionVehicleRepository()
	approvalRepository := approvalPersistence.NewApprovalRepository()
	assetStatusRequestRepo := assetServicePersistence.NewAssetStatusRequestRepository()
	bonusPenaltyRepo := assetServicePersistence.NewBonusPenaltyRepository()
	packageRepository := packagePersistence.NewPackageRepository()
	alertRepo := integrationPersistence.NewAlertRepository()
	integrationRepo := integrationPersistence.NewIntegrationRepository()
	orderRepository := persistence.NewOrderRepository()
	vehicleTargetTyreRemovalRepo := assetServicePersistence.NewVehicleTargetTyreRemovalRepository()

	// Utils
	assetInpectionUtil := assetUtils.NewAssetInspectionUtil()

	// Use Cases
	formUseCase := contentUsecase.NewFormUsecase(dbUsecase, formRepository, ticketRepository)
	financeUsecase := financeUseCase.NewFinanceUsecase(
		dbUsecase,
		financeRepo,
	)
	notificationUc := notificationUsecase.NewNotificationUsecase(
		dbUsecase,
		notificationRepo,
		emailRepo,
		userIdentityRepository,
	)
	attachmentUseCase := storageUsecase.NewAttachmentUseCase(dbUsecase, attachmentRepository, storageRepository)
	ticketUseCase := taskUsecase.NewTicketUseCase(
		dbUsecase, ticketRepository, userIdentityRepository, assetRepository,
		financeUsecase,
		assetTyreRepository,
		attachmentUseCase,
		assetVehicleRepository,
		assetAssignmentRepository,
		departmentRepository,
		assetComponentRepo,
		assetInspectionRepo,
		assetServicePersistence.NewLocationRepository(),
		orderRepository,
		storageRepository,
	)
	approvalUsecase := approvalUsecase.NewApprovalUseCase(dbUsecase, approvalRepository, assetRepository, userIdentityRepository, assetStatusRequestRepo, bonusPenaltyRepo, assetLinkedRepository, financeUsecase, packageRepository, assetTyreRepository)
	assetUseCase := assetServiceUsecase.NewAssetUseCase(
		dbUsecase,
		assetRepository,
		userIdentityRepository,
		assetVehicleRepository,
		assetLogRepository,
		assetTyreRepository,
		assetAssignmentRepository,
		attachmentUseCase,
		formUseCase,
		&approvalUsecase,
		alertRepo,
		assetLinkedRepository,
		integrationRepo,
		assetTyreRepository,
		assetInspectionTyreRepository,
		vehicleTargetTyreRemovalRepo,
	)
	assetUseCase.SetNotifUseCase(notificationUc)
	assetAssignmentUseCase := assetServiceUsecase.NewAssetAssignmentUseCase(
		dbUsecase,
		assetAssignmentRepository, userIdentityRepository, assetLinkedRepository,
		notificationUc,
		assetRepository,
		attachmentUseCase,
		formRepository,
		assetVehicleRepository,
		assetInspectionRepo,
		assetInspectionTyreRepository,
		assetInspectionVehicleRepository,
		assetInpectionUtil,
		assetTyreRepository,
		integrationRepo,
	)

	// Update Usecases
	assetUseCase.UpdateAssetAssignmentUseCase(assetAssignmentUseCase)
	ticketUseCase.SetAssetUseCase(assetUseCase)
	ticketUseCase.SetNotifUseCase(&notificationUc)

	// Handlers
	ticketHandler := taskHandlr.NewTicketHandler(ticketUseCase, attachmentUseCase)

	// Register Routes
	taskRouters.RegisterTicketRoutes(router, ticketHandler)

	return nil
}
