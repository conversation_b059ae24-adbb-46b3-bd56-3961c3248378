package main

import (
	"assetfindr/cmd/approvalservice"
	"assetfindr/cmd/assetservice"
	"assetfindr/cmd/contentservice"
	"assetfindr/cmd/geoservice"
	"assetfindr/cmd/integrationservice"
	"assetfindr/cmd/inventoryservice"
	"assetfindr/cmd/logservice"
	"assetfindr/cmd/notificationservice"
	"assetfindr/cmd/storageservice"
	"assetfindr/cmd/taskservice"
	truphoneservice "assetfindr/cmd/truphoneService"
	"assetfindr/cmd/uploadservice"
	"assetfindr/cmd/useridentityservice"
	config "assetfindr/internal/config"
	"assetfindr/internal/constants"
	bq "assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/cloudStorage"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/email"
	"assetfindr/internal/infrastructure/firebaseApp"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/routers"
	"os"
)

func main() {

	if err := config.SetupConfig(); err != nil {
		commonlogger.Fatalf("config SetupConfig() error: %s", err)
	}

	masterDSN, replicaDSN := config.DbConfiguration()
	masterBQProjectID := config.BQConfiguration()

	if err := firebaseApp.InitFirebaseClients(); err != nil {
		commonlogger.Fatalf("config Firebase App error: %s", err)
	}

	if err := cloudStorage.CreateCloudStorageClient(); err != nil {
		commonlogger.Fatalf("config Cloud Storage error: %s", err)
	}

	if err := database.DbConnection(masterDSN, replicaDSN); err != nil {
		commonlogger.Fatalf("database DbConnection error: %s", err)
	}

	timeseriesDBDsn := config.TimeseriesDbConfig()
	err := database.TimeseriesDbConnect(timeseriesDBDsn)
	if err != nil {
		commonlogger.Fatalf("timeseries database connection error:", err)
	}

	if err := bq.BQConnection(masterBQProjectID); err != nil {
		commonlogger.Warnf("Bigquery connection error: %s", err)
	}

	err = database.BigqueryDbConnect()
	if err != nil {
		commonlogger.Warnf("Bigquery connection error: %s", err)
	}

	helpers.InitValidator()

	if err := email.SetupEmailServices(); err != nil && os.Getenv(constants.ENV_APP_ENV) != "local" {
		commonlogger.Fatalf("Failed to create email services:%v\n", err)
		return
	}

	router := routers.SetupRoute()

	router.Static("/statics", "./statics")

	useridentityservice.Register(router)
	assetservice.Register(router)
	taskservice.Register(router)
	storageservice.Register(router)
	notificationservice.Register(router)
	approvalservice.Register(router)
	contentservice.Register(router)
	uploadservice.Register(router)
	logservice.Register(router)
	inventoryservice.Register(router)
	integrationservice.Register(router)
	geoservice.Register(router)
	truphoneservice.Register(router)

	commonlogger.Fatalf("%v", router.Run(config.ServerConfig()))

}
